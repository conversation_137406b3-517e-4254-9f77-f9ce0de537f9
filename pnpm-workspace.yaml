packages:
  - packages/*
  - playground/*

catalog:
  '@clack/prompts': ^0.11.0
  '@eslint/js': ^9.31.0
  '@prettier/plugin-pug': ^3.4.0
  '@types/cross-spawn': ^6.0.6
  '@types/js-yaml': ^4.0.9
  '@types/fs-extra': ^11.0.4
  '@types/mkdirp': ^1.0.2
  '@types/node': ^22.16.5
  '@types/pug': ^2.0.10
  autoprefixer: ^10.4.21
  commander: ^11.1.0
  cross-spawn: ^7.0.6
  dayjs: ^1.11.13
  js-yaml: ^4.1.0
  eslint: 9.31.0
  eslint-config-prettier: ^10.1.1
  eslint-plugin-only-warn: ^1.1.0
  eslint-plugin-react: ^7.37.5
  eslint-plugin-react-hooks: ^5.2.0
  eslint-plugin-turbo: ^2.5.0
  fs-extra: ^11.3.0
  globals: ^16.3.0
  lint-staged: ^16.1.2
  mkdirp: ^1.0.4
  node-html-parser: ^7.0.1
  picocolors: 1.1.1
  postcss: ^8.5.6
  prettier: 3.6.2
  pug: ^3.0.3
  pug-error: ^2.1.0
  query-string: ^9.2.2
  rollup: ^4.46.2
  sharp: ^0.34.3
  simple-git-hooks: ^2.13.0
  tsup: ^8.5.0
  turbo: ^2.5.5
  typescript: ^5.8.3
  typescript-eslint: ^8.36.0
  unocss: ^66.3.3
  vite: ^6.3.5

