{"name": "tiga", "private": true, "type": "module", "scripts": {"build": "turbo build --filter=!./playground/*", "dev": "turbo dev --filter=!./playground/*", "lint": "prettier --check packages && turbo lint", "format": "prettier --write packages"}, "devDependencies": {"@tiga/eslint-config": "workspace:*", "@types/node": "catalog:", "eslint": "catalog:", "lint-staged": "catalog:", "prettier": "catalog:", "simple-git-hooks": "catalog:", "tsup": "catalog:", "turbo": "catalog:", "typescript": "catalog:"}, "packageManager": "pnpm@10.12.4", "engines": {"node": ">=18"}, "simple-git-hooks": {"pre-commit": "npx lint-staged", "pre-push": "pnpm format"}, "lint-staged": {"*.{js,ts,tsx,vue,md}": ["eslint --cache --fix"]}}