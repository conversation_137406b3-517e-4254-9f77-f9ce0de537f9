<!-- 更新于: 2025-01-08 -->
<!DOCTYPE html>
<html lang="en-US" from="Venus-Render" dir="ltr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OnePlus 13</title>
  <meta name="keywords" content="OnePlus 13"/>
  <meta name="description" content="OnePlus 13"/>
  <link rel="canonical" href="https://www.oneplus.com/us/13">
  <link rel="icon" href="https://oasis.opstatics.com/content/dam/oasis/oneplus.ico" type="image/x-icon"/>
  <link rel="shortcut icon" href="https://oasis.opstatics.com/content/dam/oasis/oneplus.ico" type="image/x-icon"/>
  <link rel="stylesheet" href="https://cdn.opstatics.com/jimu/global-store/client/chunk-pharos-ui.css?v=1735558881491" type="text/css">
  <link rel="stylesheet" href="https://cdn.opstatics.com/jimu/global-store/client/chunk-vendors.css?v=1735558881491" type="text/css">
  <link rel="stylesheet" href="https://cdn.opstatics.com/jimu/global-store/client/oneplus_main.css?v=1735558881491" type="text/css">
</head>
<!--/* Universal loading  */-->
<style type="text/css" id="universal-loading-canvas-style">
  body,html {
    overflow-x: hidden
  }

  
  .header-v3,.vue-comp-container{
    display: none !important;
  }

  .universal-loading-canvas {
    position: fixed;
    top: 0;
    height: 100%;
    width: 100%
  }

  .universal-loading-canvas .univesal-loading-container {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%)
  }

  .univesal-loading-container {
    display: block;
    width: 48px;
    height: 48px
  }

  @media screen and (max-width: 735px) {
    .univesal-loading-container {
      width:40px;
      height: 40px
    }
  }

  .univesal-loading-container .universal-spinner {
    -webkit-animation: 1.4s linear infinite rotator;
    animation: 1.4s linear infinite rotator;
    width: 100%;
    height: 100%
  }

  .univesal-loading-container .path {
    stroke-dasharray: 187;
    stroke-dashoffset: 0;
    -webkit-transform-origin: center;
    transform-origin: center;
    stroke: #000;
    -webkit-animation: 1.4s ease-in-out infinite dash;
    animation: 1.4s ease-in-out infinite dash
  }

  @-webkit-keyframes rotator {
    0% {
      -webkit-transform: rotate(0);
      transform: rotate(0)
    }

    to {
      -webkit-transform: rotate(270deg);
      transform: rotate(270deg)
    }
  }

  @keyframes rotator {
    0% {
      -webkit-transform: rotate(0);
      transform: rotate(0)
    }

    to {
      -webkit-transform: rotate(270deg);
      transform: rotate(270deg)
    }
  }

  @-webkit-keyframes white-colors {
    0%,25%,50%,75%,to {
      stroke: #fff
    }
  }

  @keyframes white-colors {
    0%,25%,50%,75%,to {
      stroke: #fff
    }
  }

  @-webkit-keyframes dash {
    0% {
      stroke-dashoffset: 187
    }

    50% {
      stroke-dashoffset: 46.75;
      -webkit-transform: rotate(135deg);
      transform: rotate(135deg)
    }

    to {
      stroke-dashoffset: 187;
      -webkit-transform: rotate(450deg);
      transform: rotate(450deg)
    }
  }

  @keyframes dash {
    0% {
      stroke-dashoffset: 187
    }

    50% {
      stroke-dashoffset: 46.75;
      -webkit-transform: rotate(135deg);
      transform: rotate(135deg)
    }

    to {
      stroke-dashoffset: 187;
      -webkit-transform: rotate(450deg);
      transform: rotate(450deg)
    }
  }

  .visuallyhidden {
    position: absolute;
    clip: rect(1px, 1px, 1px, 1px);
    -webkit-clip-path: inset(0 0 99.9% 99.9%);
    clip-path: inset(0 0 99.9% 99.9%);
    overflow: hidden;
    height: 1px;
    width: 1px;
    padding: 0;
    border: 0;
  }
</style>
<section class="universal-loading-canvas" id="universal-loading-canvas">
  <i class="univesal-loading-container">
    <svg class="universal-spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg">
      <circle class="path" fill="none" stroke-width="6" stroke-linecap="round" cx="33" cy="33" r="30"></circle>
    </svg>
  </i>
</section>
<body class="cms-index-index cms-home hide-body">
<h1 class="visuallyhidden">OnePlus 13</h1>
<input type="hidden" id="mark-current-store" name="mark-current-store" value="us">
<style>
  .categories-nav .tab-content .tab .swiper .swiper-wrapper .swiper-slide::after {
    content: '';
  }

  @media screen and (max-width: 1023px) {
    .categories-nav .tab-content .tab .swiper .swiper-wrapper .swiper-slide::after {
      display: none;
    }
  }

  .cmp__product-shelf-jimu .product-shelf-container .card-item-swiper-container {
    margin: 0 auto;
  }
</style>
<script>
  window.subscribePrivacy = {
    privacyDesc: "Yes, I would like to receive a marketing email from OnePlus and agree to the ",
    privacyPolicy: "privacy policy",
    dialogTitle: "Subscription Notice",
    dialogDesc: "OnePlus may create personal profiles from my purchase and usage behavior.<br>This means that advertising and websites are better tailored to my personal interests.<br>Please read and agree to the",
    dialogCancelBtn: "Disagree",
    dialogConfirmBtn: "Agree and subscribe",
    privacyLink: "https://www.oneplus.com/us/legal/us-privacy-policy"
  };
  window.isNewLoginConfig = {
    callbackUrl: window.location.origin + '/account/login',
    bizAppKey: 'DkPXNvHbK1uQrd4nG5drut',
    environment: 'prod',
    userCenter: 'https://accounts.oneplus.com/v2/profile.html'
  };
  ;window.op = window.op || {};
  window.op.initTime = new Date();
  window.isEncrypt = true;
  window.isXman = false;
  window.isXmanObj = {
    mallTradeCartInviteCheck: true,
    mallTradeCartFetch: true,
    mallTradeCartAdd: true,
    mallProductDetailFetch: true,
    mallTradeCartUpdate: true,
    mallTradeCartGiftFetch: true,
    mallTradeCartRecommendFetch: true,
    mallTradeCheckoutInit: true,
    mallTradeCheckoutSettle: true,
    mallTradeCheckoutSubmit: true,
    mallTradeCheckoutCaptchaCheck: true,
    mallTradeCheckoutCaptchaRefresh: true,
    mallTradeCheckoutCurrencyCheck: true
  };
  window.mallUrl = "https://mallapi-na.oneplus.com/v2/api/router";
  window.emialUseMall = true;
  window.isXmanOffInvite = true;
  window.isXmanOffImei = true;
  window.isXmanOffDevice = false;
  window.isXmanOffPay = true;
  window.isXmanOff = true;
  window.isXmanOffTradeIn = false;
  window.isXmanEnd = true;
  window.isXmanOffSubscribeEmail = true;
  window.isXmanOffSendCode = true;
  window.rccPlanOrderCheckout = true;
  ;window.isWcsmOpen = false;
  window.closeReplaceEntry = true;
  window.isAnnouncementOpen = false;
  window.isPreLoadLoginPopup = true;
  window.preLoadLoginPopupSrc = 'https://accounts.oneplus.com/v2/popper.html?bizAppKey=DkPXNvHbK1uQrd4nG5drut&callback=https%3A%2F%2Fwww.oneplus.com%2Faccount%2Flogin';
  window.preLoadHtmlSrc = 'https://accounts.oneplus.com/v2/popper.html?bizAppKey=DkPXNvHbK1uQrd4nG5drut&callback=https%3A%2F%2Fwww.oneplus.com%2Faccount%2Flogin'
</script>
<script async src="https://id.heytap.com/packages/account_web_sdk/index.umd.js"></script>
<style>
  #header.new-nav .icon-header.ico-header-logo {
    margin-right: 0;
  }

  #header.new-nav .user-info-nav .user-info-content .user-avatar {
    display: flex;
  }

  @media screen and (max-width: 1024px) {
    #header.new-nav.nav-fixed .nav .nav-right .header-search.show-in-mobile {
      display: none;
    }
  }
</style>
<script>
  window.isCommunity = !!(typeof window.COMMUNITY_APP_ACCOUNT !== 'undefined' && typeof window.COMMUNITY_APP_ACCOUNT.COMMUNITY_APP_ACCOUNT_LOGIN !== 'undefined' && typeof window.COMMUNITY_APP_ACCOUNT.COMMUNITY_APP_ACCOUNT_LOGIN === 'function')
</script>
<script>
  window.obusCountryCode = "us";
  window.isAccessObus = true;
  window.isProductionEnv = 'https://www.oneplus.com/us'.indexOf('test') == -1 && 'https://www.oneplus.com/us'.indexOf('dev') == -1
</script>
<style>
  .choose-store dl dd li .store-currency {
    width: 20%
  }

  .choose-store dl dd li .store-lang {
    width: 30%
  }
</style>
<style>
  .payments-container .payments.payments-india em {
    color: #000;
  }

  @media screen and (min-width: 769px) {
    .container-text {
      max-width: 90pc;
    }
  }

  .nav-bread {
    width: 100%;
    display: flex;
    width: 100%;
    display: flex;
    color: #000;
    font-size: 16px;
    min-height: auto;
    line-height: inherit;
  }

  .home {
    display: inline-block;
    margin-right: 12px;
    padding: 13px 0;
  }

  .home a {
    text-decoration: none;
    color: #000;
  }

  .bread-link {
    color: #000;
  }

  .bread-ol {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    margin: 0;
    list-style: none;
  }

  .bread-ol li {
    padding: 13px 0;
  }

  .bread-ol li span {
    display: inline-block;
    margin: 0 12px;
  }

  .bread-ol li .bread-goods-name {
    display: inline-block;
    margin: 0 12px;
    font-size: 16px;
  }

  .bread-ol li a {
    color: #000;
  }

  .bread-ol li:before {
    content: "";
    width: 15px;
    height: 20px;
    background-size: 8px 18px;
    display: inline-block;
    vertical-align: sub;
    background: url("https://image01.oneplus.net/shop/201903/21/1591/5c27720e66ff7447e055c6f86f8907b5.svg") center no-repeat;
  }

  @media screen and (max-width: 768px) {
    .icon-svg {
      display: none;
    }

    .nav-bread {
      width: 100%;
      display: flex;
      width: 100%;
      display: flex;
      color: #000;
    }

    .payments.payments-india.no-wrap {
      white-space: unset;
    }
  }

  @media screen and (min-width: 736px) {
    .payments-and-delivery.payments-and-delivery-india .logo-icons {
      text-align: left;
    }
  }

  .our-phone .logo-icons i {
    margin-bottom: 5px;
  }

  #header .nav .nav-pages .page-order {
    display: none;
  }

  @media screen and (max-width: 1050px) {
    #header .nav .nav-pages .page-order, #header .nav .nav-pages .page-member:after {
      display: block;
    }
  }

  @media screen and (max-width: 1050px) {
    #header .nav .nav-pages .page-order:after {
      display: none;
    }
  }
</style>
<style type="text/css" id="universal-loading-canvas-style">
  .hide-body {
    height: 0;
    overflow: hidden
  }

  body,html {
    overflow-x: hidden
  }

  .universal-loading-canvas {
    position: fixed;
    top: 0;
    height: 100%;
    width: 100%
  }

  .universal-loading-canvas .univesal-loading-container {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%)
  }

  .univesal-loading-container {
    display: block;
    width: 48px;
    height: 48px
  }

  @media screen and (max-width: 735px) {
    .univesal-loading-container {
      width:40px;
      height: 40px
    }
  }

  .univesal-loading-container .universal-spinner {
    -webkit-animation: 1.4s linear infinite rotator;
    animation: 1.4s linear infinite rotator;
    width: 100%;
    height: 100%
  }

  .univesal-loading-container .path {
    stroke-dasharray: 187;
    stroke-dashoffset: 0;
    -webkit-transform-origin: center;
    transform-origin: center;
    stroke: #000 !important;
    -webkit-animation: 1.4s ease-in-out infinite dash;
    animation: 1.4s ease-in-out infinite dash
  }

  @-webkit-keyframes rotator {
    0% {
      -webkit-transform: rotate(0);
      transform: rotate(0)
    }

    to {
      -webkit-transform: rotate(270deg);
      transform: rotate(270deg)
    }
  }

  @keyframes rotator {
    0% {
      -webkit-transform: rotate(0);
      transform: rotate(0)
    }

    to {
      -webkit-transform: rotate(270deg);
      transform: rotate(270deg)
    }
  }

  @-webkit-keyframes white-colors {
    0%,25%,50%,75%,to {
      stroke: #fff
    }
  }

  @keyframes white-colors {
    0%,25%,50%,75%,to {
      stroke: #fff
    }
  }

  @-webkit-keyframes dash {
    0% {
      stroke-dashoffset: 187
    }

    50% {
      stroke-dashoffset: 46.75;
      -webkit-transform: rotate(135deg);
      transform: rotate(135deg)
    }

    to {
      stroke-dashoffset: 187;
      -webkit-transform: rotate(450deg);
      transform: rotate(450deg)
    }
  }

  @keyframes dash {
    0% {
      stroke-dashoffset: 187
    }

    50% {
      stroke-dashoffset: 46.75;
      -webkit-transform: rotate(135deg);
      transform: rotate(135deg)
    }

    to {
      stroke-dashoffset: 187;
      -webkit-transform: rotate(450deg);
      transform: rotate(450deg)
    }
  }
</style>
<section class="universal-loading-canvas" id="universal-loading-canvas">
  <i class="univesal-loading-container">
    <svg class="universal-spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg">
      <circle class="path" fill="none" stroke-width="6" stroke-linecap="round" cx="33" cy="33" r="30"></circle>
    </svg>
  </i>
</section>
<script>
  (function(w, d, s, l, i) {
      w[l] = w[l] || [];
      w[l].push({
        'gtm.start': new Date().getTime(),
        event: 'gtm.js'
      });
      var f = d.getElementsByTagName(s)[0]
        , j = d.createElement(s)
        , dl = l != 'dataLayer' ? '&l=' + l : '';
      j.async = true;
      j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
      f.parentNode.insertBefore(j, f);
    }
  )(window, document, 'script', 'dataLayer', 'GTM-5HW9RGK');
</script>
<noscript>
  <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5HW9RGK" height="0" width="0" style="display:none;visibility:hidden"></iframe>
</noscript>
<input type="hidden" id="local_price_format" value="%7B%22pattern%22%3A%22%24%25s%22%2C%22precision%22%3A2%2C%22requiredPrecision%22%3A2%2C%22decimalSymbol%22%3A%22.%22%2C%22groupSymbol%22%3A%22%2C%22%2C%22groupLength%22%3A3%2C%22integerRequired%22%3A1%7D">
<input type="hidden" id="mark-currency-format" name="mark-currency-format" value="%7B%22pattern%22%3A%22%24%25s%22%2C%22precision%22%3A2%2C%22requiredPrecision%22%3A2%2C%22decimalSymbol%22%3A%22.%22%2C%22groupSymbol%22%3A%22%2C%22%2C%22groupLength%22%3A3%2C%22integerRequired%22%3A1%7D">
<input type="hidden" id="_current_store" value="us">
<input type="hidden" id="link-home" name="link-home" value="https://www.oneplus.com/us/">
<input type="hidden" id="_india_host" value="https://www.oneplus.in">
<input type="hidden" id="_net_host" value="https://www.oneplus.com">
<input type="hidden" id="_cn_host" value="https://www.oneplus.com/cn">
<input type="hidden" id="_in_account" value="https://account.oneplus.in">
<input type="hidden" id="_com_account" value="https://account.oneplus.com">
<input type="hidden" id="assets-public-path" name="assets-public-path" value="https://cdn.opstatics.com">
<input type="hidden" id="sign-in-url" name="sign-in-url" value="https://account.oneplus.com/json/store/signIn">
<input type="hidden" id="link-user-center" name="link-user-center" value="https://www.oneplus.com/us/customer/info">
<input type="hidden" id="mark-current-store" name="mark-current-store" value="us">
<input type="hidden" name="mark-currency-code" id="mark-currency-code" value="USD">
<input type="hidden" name="risk-jump-url" id="risk-jump-url" value="https://www.oneplus.com/us/account/sign-in/security_warning">
<input type="hidden" id="user-behavior-url" name="user-behavior-url" value="https://mallapi-na.oneplus.com/v2/api/router">
<input type="hidden" id="mall-api-url" name="mall-api-url" value="https://mallapi-na.oneplus.com/v2/api/router">
<input type="hidden" id="member-api-url" name="member-api-url" value="https://memberapi-na.oneplus.com/v2/api/router">
<input type="hidden" id="membership-api-url" name="membership-api-url" value="https://membershipapi-na.oneplus.com/api/2.0">
<input type="hidden" id="mall-rest-url" name="mall-rest-url" value="https://mallapi-na.oneplus.com/v2/api/rest">
<input type="hidden" id="mall-api-url-rest" name="mall-api-url" value="https://mallapi-na.oneplus.com/v2/api/rest">
<input type="hidden" id="domain_rest_levin" name="domain_rest_levin" value="https://mallapi-na.oneplus.com/v2/api/rest">
<script type="text/javascript">
  var onloadCallback = function() {
    $('#g_recaptcha').removeClass('hidden');
    grecaptcha.render('g_recaptcha_placeholder', {
      'sitekey': '6LcXjQcUAAAAANKDft8a25E8PRogqCPKENK2LCnv',
      'theme': 'white',
      'tabindex': 5,
      'size': 'width: 304px; height: 80px;'
    });

  };
</script>
<script type="application/json" id="header-data-email-list">
  [
    "@gmail.com",
    "@hotmail.com",
    "@yahoo.com",
    "@outlook.com",
    "@yahoo.co.in",
    "@googlemail.com",
    "@hotmail.co.uk",
    "@hotmail.fr",
    "@yahoo.in",
    "@hotmail.it",
    "@ymail.com",
    "@msn.com",
    "@yahoo.co.uk",
    "@mail.ru",
    "@hotmail.com",
    "@foxmail.com"
  ]</script>
</script><script type="application/json" id="header-data-email-err-list">
  {
    "180085": "Oops! Please try again.",
    "180082": "Subscribe too many. Try again later.",
    "180080": "Oops, you have already subscribed."
  }</script>
<script type="application/json" id="header-data-translation">
  {
    "emailTooFast": "Subscribe too many. Try again later.",
    "emailFail": "Oops! Please try again.",
    "emailPlaceholder": "Email address",
    "phonePlaceholder": "Enter phone number",
    "emailOrPhonePlaceholder": "Your e-mail or phone number",
    "phonePlaceholder": "Enter phone number",
    "emailOrPhonePlaceholder": "Your e-mail or phone number",
    "emailEmptyErrorTip": "The email address field cannot be empty.",
    "emailInvalidErrorTip": "Invalid email address. Please retry.",
    "emailSystemErrorTip": "System error, please try again later.",
    "emailSubscribedErrorTip": "Oops, you have already subscribed.",
    "emailFrequentErrorTip": "System busy, please try again later.",
    "succeedTip": "Thanks for subscribing!",
    "source": "auto-subscribe-source",
    "listId": "7a427be021",
    "event": "Subscribe",
    "form": "Subscription_for_Gift",
    "event_click": "Subscribe_Click",
    "form_click": "Gift_for_Subscription_Ribbon_Click",
    "iconImg": "https://image01.oneplus.net/shop/201811/15/1755/5ccb72c2de1ce47220a0e1fad8e79915.png",
    "day": 7,
    "twitterUrl": "https://twitter.com/OnePlus_USA",
    "instagramUrl": "https://www.instagram.com/oneplus_usa/",
    "facebookUrl": "https://business.facebook.com/oneplusna",
    "forumUrl": "https://forums.oneplus.com",
    "isReportData": false,
    "questionTips": "How would you rate your experience on this page？",
    "shareFeedBack": "Rate now"
  }</script>
<script type="application/json" id="header-data-url-list">
  [
  ]</script>
<script>
  window.AJAX_OPTIONS = {
    timeout: 60000,
    tips: 'Oops, the spaceship just got lost! We are trying to get it back to earth.'
  }
</script>
<script type="application/json" id="data-top-navigation">
  {
    "topNav": {
      "left": [
        {
          "name": "Phones",
          "url": "https://www.oneplus.com/us/store/phone",
          "secondNav": [
            {
              "name": "OnePlus 12",
              "url": "https://www.oneplus.com/us/oneplus-12",
              "imgUrl": "https://oasis.opstatics.com/content/dam/oasis/page/2024/nav/in/waffle-green.png",
              "new": "New"
            },
            {
              "name": "OnePlus 12R",
              "url": "https://www.oneplus.com/us/oneplus-12r",
              "imgUrl": "https://oasis.opstatics.com/content/dam/oasis/page/2024/nav/in/aston-blue.png",
              "new": "New"
            },
            {
              "name": "OnePlus Open",
              "url": "https://www.oneplus.com/us/oneplus-open",
              "imgUrl": "https://oasis.opstatics.com/content/dam/oasis/page/2023/nav/green-pc2.png",
              "new": "New"
            },
            {
              "name": "OnePlus Nord N30 5G",
              "url": "https://www.oneplus.com/us/oneplus-n30-5g",
              "imgUrl": "https://oasis.opstatics.com/content/dam/oasis/page/2023/nav/nav-larry-black.png",
              "new": ""
            }
          ],
          "linkList": [
            {
              "text": "Flagship Series",
              "url": "https://www.oneplus.com/us/store/phone?groupName=Series&sortName=Flagship%20Series"
            },
            {
              "text": "OnePlus Nord N Series",
              "url": "https://www.oneplus.com/us/store/phone?groupName=Series&sortName=Nord%20Series"
            }
          ],
          "buttonList": [
            {
              "btnText": "Compare",
              "link": "https://www.oneplus.com/us/phone/compare",
              "type": "ghost"
            },
            {
              "btnText": "View All",
              "link": "https://www.oneplus.com/us/store/phone",
              "type": "secondary"
            }
          ]
        },
        {
          "name": "Audio",
          "url": "https://www.oneplus.com/us/store/audio",
          "secondNav": [
            {
              "name": "OnePlus Buds 3",
              "new": "New",
              "url": "https://www.oneplus.com/us/product/oneplus-buds-3",
              "imgUrl": "https://oasis.opstatics.com/content/dam/oasis/page/2024/nav/in/eular.png"
            },
            {
              "name": "OnePlus Nord Buds 2",
              "new": "",
              "url": "https://www.oneplus.com/us/product/oneplus-nord-buds-2",
              "imgUrl": "https://oasis.opstatics.com/content/dam/oasis/page/2023/nav/tesla1.png"
            },
            {
              "name": "OnePlus Buds Pro 2",
              "new": "",
              "url": "https://www.oneplus.com/us/product/oneplus-buds-pro-2",
              "imgUrl": "https://oasis.opstatics.com/content/dam/oasis/page/2023/nav/OnePlusBudsPro2.png"
            },
            {
              "name": "OnePlus Nord Wired Earphones",
              "new": "",
              "url": "https://www.oneplus.com/us/product/oneplus-nord-wired-earphones",
              "imgUrl": "https://oasis.opstatics.com/content/dam/oasis/page/2022/new-navi/nord-wired-earphones/Edison.png"
            }
          ],
          "buttonList": [
            {
              "btnText": "Compare",
              "link": "https://www.oneplus.com/us/audio/compare",
              "type": "ghost"
            },
            {
              "btnText": "View All",
              "link": "https://www.oneplus.com/us/store/audio",
              "type": "secondary"
            }
          ]
        },
        {
          "name": "Tablet",
          "url": "https://www.oneplus.com/us/store/tablet",
          "secondNav": [
            {
              "name": "OnePlus Pad",
              "new": "new",
              "url": "https://www.oneplus.com/us/buy-oneplus-pad",
              "imgUrl": "https://oasis.opstatics.com/content/dam/oasis/page/2023/nav/pad-nav.png"
            },
            {
              "name": "Pad Accessories",
              "new": "",
              "url": "https://www.oneplus.com/us/store/tablet",
              "imgUrl": "https://oasis.opstatics.com/content/dam/oasis/page/2023/nav/shelltopnav.png"
            }
          ]
        },
        {
          "name": "Wearables",
          "url": "https://www.oneplus.com/us/store/wearables",
          "secondNav": [
            {
              "name": "OnePlus Watch 2",
              "new": "new",
              "url": "https://www.oneplus.com/us/buy-oneplus-watch-2",
              "imgUrl": "https://oasis.opstatics.com/content/dam/oasis/page/2024/nav/common/almond-silver.png"
            }
          ]
        },
        {
          "name": "Accessories",
          "url": "https://www.oneplus.com/us/store/power-cables",
          "secondNav": [
            {
              "name": "Cases & Protection",
              "new": "",
              "url": "https://www.oneplus.com/us/store/cases-protection",
              "imgUrl": "https://oasis.opstatics.com/content/dam/oasis/page/2024/nav/us/waffle-case.png"
            },
            {
              "name": "Power & Cables",
              "new": "",
              "url": "https://www.oneplus.com/us/store/power-cables",
              "imgUrl": "https://oasis.opstatics.com/content/dam/oasis/page/events/ovaltine/Power-Cables.png"
            },
            {
              "name": "Bundles",
              "new": "",
              "url": "https://www.oneplus.com/us/store/bundles",
              "imgUrl": "https://oasis.opstatics.com/content/dam/oasis/page/events/ovaltine/Bundles.png"
            },
            {
              "name": "Keyboard 81 Pro",
              "new": "",
              "url": "https://www.oneplus.com/us/product/keyboard-81-pro",
              "imgUrl": "https://oasis.opstatics.com/content/dam/oasis/page/2023/nav/keyboard81.png"
            }
          ]
        },
        {
          "name": "Offers",
          "url": "",
          "secondNav": [
            {
              "name": "Trade-in & Upgrade",
              "new": "",
              "url": "https://www.oneplus.com/us/trade-in",
              "imgUrl": "https://oasis.opstatics.com/content/dam/oasis/page/2023/nav/na-trade-in/Frame270989638.png"
            },
            {
              "name": "Student Discounts",
              "new": "",
              "url": "https://www.oneplus.com/us/discount-program?verify=education_program",
              "imgUrl": "https://oasis.opstatics.com/content/dam/oasis/page/2023/nav/na-trade-in/Frame270989639.png"
            },
            {
              "name": "Link OnePlus Devices",
              "new": "",
              "url": "https://www.oneplus.com/us/event/link-device-introduction",
              "imgUrl": "https://oasis.opstatics.com/content/dam/oasis/page/2023/nav/na-trade-in/Frame270989640-2.png"
            },
            {
              "name": "Employee Discount Program",
              "new": "",
              "url": "https://www.oneplus.com/us/employee-groups?verify=employee",
              "imgUrl": "https://oasis.opstatics.com/content/dam/oasis/page/2023/nav/na-trade-in/Frame270989640-1.png"
            }
          ]
        }
      ],
      "right": [
        {
          "name": "Store",
          "url": "https://www.oneplus.com/us/store"
        },
        {
          "name": "OneTopia",
          "url": "https://www.oneplus.com/us/onetopia"
        },
        {
          "name": "OnePlus Featuring",
          "url": "https://www.oneplus.com/us/oneplus-featuring"
        },
        {
          "name": "Community",
          "url": "https://forums.oneplus.com"
        },
        {
          "name": "Support",
          "url": "https://www.oneplus.com/us/support"
        }
      ]
    },
    "userMenu": [
      {
        "text": "Orders",
        "url": "https://www.oneplus.com/us/check-order?from=head",
        "iconName": "ico-header-order"
      },
      {
        "text": "Account",
        "url": "https://www.oneplus.com/us/customer/info?from=head",
        "iconName": "ico-header-account"
      },
      {
        "text": "Achievement",
        "url": "https://www.oneplus.com/us/rcc/badge?from=head",
        "iconName": "ico-badge"
      },
      {
        "text": "RedCoins",
        "url": "https://www.oneplus.com/us/redcoins-center#/",
        "iconName": "ico-header-redcoins",
        "hidden": ""
      },
      {"text": "Red Cable Club",
        "url": "https://www.oneplus.com/us/redcableclub",
        "iconName": "ico-header-rcc",
        "hidden": ""
      }
    ],
    "hideUserCard": "",
    "hideShoppingCart": "",
    "allNavRight": ""
  }</script>
<script type="application/json" id="signin-locale-data">
  {
    "signInPage": {
      "creatRegister": "Your phone number is not registered. Would you like to register now ?",
      "registerTerms": "I agree to <a href=\"https://www.oneplus.com/us/legal/user-agreement\" class=\"link-blue\" target=\"_blank\">User Agreement</a>. Please see our <a href=\"https://www.oneplus.com/us/legal/privacy-policy\" class=\"link-blue\" target=\"_blank\">Privacy Policy</a>.",
      "creatRegisterBtn": "Creat and log in",
      "signInTitle": "Welcome",
      "bindMobileTitle": "Bind Mobile Number",
      "verifyMobileTitle": "Confirm Mobile Number",
      "verifyEmailTitle": "Confirm Your Email",
      "verifyAccountTitle": "Confirm Your Account",
      "createPwdTitle": "Create Your Password",
      "signInSubtitle": "Please use OnePlus account to log in.",
      "signInTips": "Or login with",
      "createAccount": "Create Account",
      "signIn": "Sign in",
      "signUp": "Sign up",
      "password": "Password",
      "confirm": "Confirm",
      "cancel": "Cancel",
      "facebook": "Facebook",
      "google": "Google",
      "forgotPassword": "Forgot password",
      "email": "Email",
      "emailOrNumber": "Email/Mobile Number",
      "mobileNumber": "Mobile Number",
      "change": "Change",
      "edit": "Edit",
      "otp": "Enter the OTP",
      "getOtp": "Get code",
      "otpTerms": "If you never register with your phone number, a new account will be created when you use OTP to login.",
      "terms": "Protected by reCAPTCHA and subject to the OnePlus <a href=\"https://www.oneplus.com/us/legal/user-agreement\" class=\"link-blue\" target=\"_blank\">User Agreement</a> and <a href=\"https://www.oneplus.com/us/legal/privacy-policy\" class=\"link-blue\" target=\"_blank\">Privacy Policy</a>.",
      "emailCode": "Email code",
      "getEmailCode": "Get email code",
      "signUseOTP": "Phone number",
      "signUsePwd": "Password",
      "verifyMobile": "Verify Mobile Number",
      "hasSendEmail": "We have sent a verification code to your",
      "hasSendCode": "We have sent a verification code to your",
      "sendAgain": "Send again",
      "later": " later",
      "sendedText": "",
      "sendSucceedToast": "Verification code is sent.",
      "sendMobileSucceed": "The verification code has been sent.",
      "sendVerifyCodeSucceed": "The verification code has been sent.",
      "bindMobileTips": "For account security and normal use, please bind your mobile number according to the Cyber ​​Security Law.",
      "verifyMobileTips": "For account security and normal use, you need to verify your mobile number.",
      "verifyEmailTips": "For account security and normal use, you need to verify your email account.",
      "kindlyRemind": "Kindly Remind",
      "richContentDialogTip": "Login failed. Your account is set to be deactivated. Please reactivate your account before logging in.",
      "closeWindow": "Close window",
      "richRecover": "Reactive account",
      "bindAgree": "By submitting this form, you agree to our <br><a href=\"{privacyLegalLink}\" class=\"text-black text-strong text-underline\">Privacy Policy</a> and <a href=\"{termsConditionsLink}\" class=\"text-black text-strong text-underline\">Terms of Use</a>",
      "format": {
        "dd": "days",
        "hh": "hour",
        "mm": "min",
        "ss": "s"
      },
      "passwordRulesTips": {
        "letterRule": "2 or more of letters, numbers and symbols",
        "lengthRule": "8 ～16 characters"
      }
    },
    "accountTips": {
      "mobileErrorTip": "Please enter a valid mobile number.",
      "emailErrorTip": "Please ensure the email follows this format: <EMAIL>",
      "mobileEmptyErrorTip": "Please enter a valid mobile number.",
      "emailEmptyErrorTip": "Please enter a valid email address.",
      "mobileOrEmailEmptyErrorTip": "Please enter a valid email address or phone number",
      "emailPatternErrorTip": "Please ensure the email follows this format: <EMAIL>",
      "emailUnConfirmTip": "Your account may not be confirmed. Check your email for the confirmation link. Please &nbsp;&nbsp;<a href=\"{verifyEmailLink}\" class=\"link-action link-underline\">Confirm</a>",
      "emailNoExistedTip": "The email does not exist. Please &nbsp;&nbsp;<a href=\"{signUpLink}\" class=\"link-action link-underline\">Sign up</a>",
      "emailDeactivatedErrorTip": "The account is deactivated.",
      "pwdErrorTip": "Password does not meet minimal requirements.",
      "pwdPatternErrorTip": "Password does not meet minimal requirements.",
      "pwdEmptyErrorTip": "Please enter a valid password.",
      "pwdFormatErrorTip": "Please enter a valid password.",
      "pwdRulesErrorTip": "Please ensure your password meets all requirements listed below.",
      "pwdNoMatchError": "Password or email is incorrect. Please try again.",
      "paramsError": "Password or email is incorrect. Please try again.",
      "tooManyAttemptsErrorTip": "Too Many Attempts. Please try again later.",
      "googleVerifyErrorTip": "Google verification error. Please try again.",
      "verifyErrorTip": "VerifyCode incorrect, please input again."
    }
  }</script>
<script>
  window.GLOBAL_ACCOUNT_CONFIG = {
    "DOMAIN_ACCOUNT_CENTER_OATH": "https://account.oneplus.com/service",
    "DOMAIN_ACCOUNT_CENTER": "https://account.oneplus.com/service",
    "DOMAIN_ACCOUNT": "https://account.oneplus.com",
    "DOMAIN_XMAN": "https://storeapi-na.oneplus.com",
    "HOME_URL": "https://www.oneplus.com/us",
    "recaptcha": {
      "size": "invisible",
      "tabindex": 5
    },
    "defaultUrl": "https://www.oneplus.com/us",
    "signInLink": "https://account.oneplus.com/signin",
    "signUpLink": "https://account.oneplus.com/signup",
    "forgotLink": "https://account.oneplus.com/forgot",
    "verifyEmailLink": "https://account.oneplus.com/signup#/email?rt=1",
    "verifyUrlLink": "https://account.oneplus.com/confirm-url",
    "setUserNameLink": "https://account.oneplus.com/update-username",
    "userAgreementLink": "https://www.oneplus.com/us/legal/user-agreement",
    "privacyPolicy": "https://www.oneplus.com/us/legal/privacy-policy",
    "privacyLegalLink": "https://www.oneplus.com/us/privacy-and-legal",
    "termsConditionsLink": "https://www.oneplus.com/us/privacy-and-legal#terms-and-conditions",
    "isGray": true,
    "isSupportOTP": false,
    "supportType": "email",
    "isSnsFacebook": false,
    loginConfig: {
      app: 10,
      client: 1
    },
    telConfig: {
      cn: {
        // 中国
        name: 'China',
        reg: /^1[1|2|3|4|5|6|7|8|9]\d{9}$/,
        prefix: '+86',
        mc: 'cn'
      },
      in: {
        // 印度
        name: 'India',
        reg: /^[6789]\d{9}$/,
        prefix: '+91',
        mc: 'in'
      },
      us: {
        // 美国
        name: 'United States',
        reg: /^[0-9-+()\s]{10}$/,
        prefix: '+1',
        mc: 'us'
      },
      uk: {
        name: 'United Kingdom',
        reg: /^[0-9(\-+)\s]{7,}$/i,
        prefix: '+44',
        mc: 'uk'
      }
    },
    grayConfig: {
      signIn: ['https://account.oneplus.com/signin', 'https://www.oneplus.com/us/account/sign-in'],
      signUp: ['https://account.oneplus.com/signup', 'https://www.oneplus.com/us/account/sign-up'],
      forgot: ['https://account.oneplus.com/forgot', 'https://www.oneplus.com/us/account/forgot-password']
    }
  }

  function checkGray(pageName, isOld) {
    var search = window.location.search
    if (window.GLOBAL_ACCOUNT_CONFIG.isGray) {
      if (isOld) {
        var grayConfig = window.GLOBAL_ACCOUNT_CONFIG.grayConfig
        if (grayConfig && grayConfig[pageName] && grayConfig[pageName][1]) {
          window.location.href = grayConfig[pageName][0] + search
        }
      }
    } else {
      if (!isOld) {
        var grayConfig = window.GLOBAL_ACCOUNT_CONFIG.grayConfig
        if (grayConfig && grayConfig[pageName] && grayConfig[pageName][1]) {
          window.location.href = grayConfig[pageName][1] + search
        }
      }
    }
  }
</script>
<script type="application/json" id="localize-domain-data">
  {
    "localizeDomain": {
      "mainapi": "https://storeapi-na.oneplus.com",
      "reviewapi": "https://reviewapi-na.oneplus.com",
      "accountapi": "https://accountapi-na.oneplus.com",
      "payapi": "https://payapi-na.oneplus.com",
      "supportapi": "https://supportapi-na.oneplus.com",
      "mallapi": "https://mallapi-na.oneplus.com"
    }
  }</script>
<link rel="stylesheet" crossorigin href="https://oasis.opstatics.com/content/dam/statics/oasis/font/aem-font/index-v3.css?version=v1"/>
<link rel="stylesheet" crossorigin href="https://cdn.opstatics.com/store/********/assets/styles/vendor.css?v=*************"/>
<script crossorigin src="https://cdn.opstatics.com/store/********/assets/scripts/vendor.js?v=*************"></script>
<script crossorigin src="https://cdn.opstatics.com/mage/scripts/vendor/jquery.js?v=*************"></script>
<input type="hidden" id="deploy-area" name="deploy-area" value="na"/>
<input type="hidden" id="levin-api-url" name="levin-api-url" value="https://levinapi-na.oneplus.com"/>
<input type="hidden" id="api_domain_member" name="api_domain_member" value="https://memberapi-na.oneplus.com"/>
<input type="hidden" id="finger-print-url" name="finger-print-url" value="https://bsp-di-us.heytapmobile.com/v1/js/d">
<input type="hidden" id="finger-print-appId" name="finger-print-appId" value="dd4e4d48b9664f8da2cc0796e4f6f9d3">
<input type="hidden" id="mall-api-rest" name="mall-api-rest" value="https://mallapi-na.oneplus.com/v2/api/rest">
<input type="hidden" id="online-chat-uri" name="online-chat-uri" value="https://oneplus.custhelp.com/app/chat/chat_launch?channel=2">
<script>
  window.ENV_CONFIG = window.ENV_CONFIG || {}
  window.__ONEPLUS_ENV_CONFIG__ = {
    // 当前商城 会携带语言
    currentStore: "us",
    // 当前国家
    currentCountryCode: "us",
    // 当前区域
    currentRegion: "na",
    // 当前货币
    currencyFormat: "%7B%22pattern%22%3A%22%24%25s%22%2C%22precision%22%3A2%2C%22requiredPrecision%22%3A2%2C%22decimalSymbol%22%3A%22.%22%2C%22groupSymbol%22%3A%22%2C%22%2C%22groupLength%22%3A3%2C%22integerRequired%22%3A1%7D",
    /** 语言包域名 */
    langDomain: 'https://cdn.opstatics.com/store/********/oneplus-json',
    // 接口域名URL
    apiBaseURL: {
      main: "",
      review: "",
      account: "https://account.oneplus.com",
      pay: "",
      support: "",
      mall: "https://mallapi-na.oneplus.com",
      member: "https://memberapi-na.oneplus.com/v2/api/router",
    },
    domain: {
      static: "https://cdn.opstatics.com",
      main: "https://www.oneplus.com/us",
      account: "https://account.oneplus.com"
    },
    // 链接
    link: {
      countryRegion: "https://www.oneplus.com/bd-address-global?v=*************"
    },
    __ONEPLUS_FP_TIME_OUT__: 300
  }
</script>
<div id="page-header">
  <online-chat></online-chat>
  <slide-question :search-result-length="hasSearchResult" :is-re-search="isReSearch"></slide-question>
  <div class="hidden-in-member-app hidden-in-store-app">
    <div class="hidden-in-bbs-app">
      <bar-store-confirm></bar-store-confirm>
    </div>
    <one-dialog no-padding ref="chooseStore">
      <choose-store></choose-store>
    </one-dialog>
  </div>
  <div id="header-auto-subscribe"></div>
  <current-limiting-dialog ref="currentLimitingDialog"></current-limiting-dialog>
  <script type="application/json" id="current-limiting-translation">
    {
      "title": "Hold on a minute!",
      "subtitle": "Too many people are trying to load this page.Please give the servers a little time to process.",
      "refresh": "Refresh page"
    }</script>
</div>
<script src="https://cdn.opstatics.com/mage/scripts/vendor/jquery.js?v=1706773921577"></script>
<script>
  window.newCheckout = {
    url: "https://www.oneplus.com/us/checkout/payment"
  }
</script>
<link href="https://www.oneplus.com/content/dam/statics/oasis/header/layout.css" rel="stylesheet"/>
<div vue-comp="headerV3" id="header">
  <div class="hidden-in-store-app hidden-in-member-app hidden-in-heytap-app">
    <header id="header-v3" class="header-v3 nav-opacity">
      <div :class="['top-header', {'fixed' : isXsMenu}]">
        <div class="nav">
          <!-- S logo -->
          <div class="nav-logo logo op-lazyload">
            <transition name="xsLogo-fade">
              <a @click="ga4Event({button_name:'Oneplus Logo', function_type:'back to homepage'}, 'function_entry')" v-show="!productMenuFlag" href="https://www.oneplus.com/us" :target="headerData.logoLinkNewPage">
                <svg class="ico svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 103 24">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M.998 3.75V24h20.25V12.438h-2.25v9.312H3.248V6h9.313V3.75H.998zM18.996 0v3.75h-3.75V6h3.75v3.75h2.25V6h3.75V3.75h-3.75V0h-2.25z"></path>
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M12.56 18.75V8.875h-2c0 .68-.229 1.192-.576 1.496-.362.304-.911.441-1.548.441h-.25v1.626h2.124v6.312h2.25zm23.264-5.087c0-2.278-1.023-4.046-3.116-4.046s-3.163 1.768-3.163 4.046c0 2.263 1.07 4.031 3.163 4.031s3.116-1.768 3.116-4.03zm-8.642 0c0-3.41 2.14-5.942 5.511-5.942 3.372 0 5.496 2.533 5.496 5.942 0 3.41-2.124 5.927-5.496 5.927-3.37 0-5.511-2.517-5.511-5.927zm12.986-5.719h2.349l3.802 6.357c.367.62.798 1.593.798 1.593h.033s-.064-1.18-.064-1.976V7.944h2.284v11.39h-2.236l-3.899-6.292c-.367-.605-.814-1.577-.814-1.577h-.032s.064 1.195.064 1.991v5.879h-2.285V7.944zm11.836 0h8.403v1.943h-6.086v2.55h5.336v1.895H54.32v3.075h6.134v1.928h-8.451V7.944zm13.086 5.385h2.396c1.214 0 1.853-.67 1.853-1.705 0-1.067-.67-1.673-1.82-1.673h-2.43v3.378zm-2.4-5.385h5.017c1.326 0 2.316.382 2.987 1.068.64.653 1.023 1.545 1.023 2.596 0 2.15-1.422 3.6-3.818 3.6h-2.812v4.127h-2.396V7.945zm10.74 0h2.316v9.463h5.368v1.928H73.43V7.944zm8.912 7.726V7.945h2.317v7.727c0 1.178.59 2.007 2.14 2.007 1.47 0 2.157-.86 2.157-2.039V7.944h2.317v7.727c0 2.437-1.502 3.855-4.425 3.855-2.956 0-4.506-1.386-4.506-3.855zm10.56.144h2.269c.16 1.37.942 1.912 2.572 1.912 1.182 0 2.22-.414 2.22-1.466 0-1.115-1.085-1.338-2.827-1.736-2.045-.462-3.914-1.004-3.914-3.378 0-2.246 1.837-3.409 4.377-3.409 2.573 0 4.234 1.258 4.41 3.553h-2.221c-.128-1.148-1.022-1.721-2.205-1.721-1.246 0-2.06.526-2.06 1.338 0 .924.798 1.195 2.492 1.562 2.348.51 4.265 1.067 4.265 3.52 0 2.31-1.869 3.6-4.44 3.6-3.1 0-4.858-1.37-4.938-3.775z"></path>
                </svg>
              </a>
            </transition>
            <transition name="xsBack-fade">
              <div v-show="productMenuFlag" class="go-back" @click="goBack">
                <svg class="ico svg-icon" viewBox="0 0 20 20">
                  <path clip-rule="evenodd" d="M13 10.41l-5.25 5.506-.861-.905L11.667 10 6.889 4.988l.861-.905L13 9.589c.22.226.22.594 0 .821z"></path>
                </svg>
              </div>
            </transition>
          </div>
          <!-- E logo -->
          <!-- S product nav -->
          <div class="nav-center">
            <ul v-if="headerData.productItems && headerData.productItems.length > 0">
              <template v-for="(item, k) in headerData.productItems">
                <li v-if="(isDtbMode && Number(item.isDtb)) || (!isDtbMode && !Number(item.isDtb)) || item.navType == 'commonNav'" class="first-product" :class="item.navStyle" @mouseenter="showSecondMenu(k, $event, item.nav)" @mouseleave="showSecondMenu(-1, $event)">
                  <a @click="ga4Event({level: 'first',nav_group: 'none',nav_name: item.navItem})" class="font-body-md first-product-name black95" :href="initLink(item.navItemLink)">{{item.nav}}</a>
                  <!-- S PC二级导航 -->
                  <transition name="nav-fade">
                    <template v-if="(item.leftItems && item.leftItems.length > 0) || (item.rightItems && item.rightItems.length > 0)">
                      <nav :class="['product-list', {'nav-show': navIndex == k}]" v-show="navIndex == k">
                        <div class="product-box">
                          <div class="list-left">
                            <div class="list-left-box list-box">
                              <a @click="ga4Event({nav_group:item.nav, method:'Big Title', nav_name:item.leftTitle, level: 'second', position: 'Left'})" v-if="item.leftTitle" :href="initLink(item.secondTitleLink)" class="list-title font-subheading-md">
                                {{item.leftTitle}}

                                <svg class="icon svg-icon arrow-icon" viewBox="0 0 20 20">
                                  <g clip-path="url(#icon-arrow-right-simple_clip0_430_12888)">
                                    <path d="M15.19 10.75H2v-1.5h13.19l-4.72-4.72 1.06-1.06L18.06 10l-6.53 6.53-1.06-1.06 4.72-4.72z"></path>
                                  </g>
                                  <defs>
                                    <clipPath id="icon-arrow-right-simple_clip0_430_12888">
                                      <path d="M0 0h20v20H0z"></path>
                                    </clipPath>
                                  </defs>
                                </svg>
                              </a>
                              <!-- 产品列表 -->
                              <div v-for="list in item.leftItems" class="product-series">
                                <a @click="ga4Event({nav_group:item.nav, method:list.title, nav_name:item.leftTitle, level: 'second', position: 'Left'})" :href="initLink(list.titleLink)" :class="[list.subItem && list.subItem.length > 0 ? 'font-note-sm black55 product-series-title' : 'emptyList font-body-md black95']">{{list.title}}</a>
                                <ul v-if="list.subItem && list.subItem.length > 0" class="product-series-main">
                                  <li v-for="product in list.subItem">
                                    <a @click="ga4Event({nav_group:item.nav, method:list.title, nav_name:product.name, level: 'second', position: 'Left'})" :href="initLink(product.link)" :target="product.newPage">
                                      <div class="img-box" v-if="product.img">
                                        <img class="op-lazyload" :data-src-2x="product.img" alt="">
                                      </div>
                                      <div class="product-name font-body-md black95">{{product.name}}</div>
                                      <div v-if="product.tag" class="product-tag font-note-xs">{{product.tag}}</div>
                                    </a>
                                  </li>
                                </ul>
                              </div>
                            </div>
                          </div>
                          <div :class="['list-right', {'list-right-empty': !item.rightTitle && !item.rightItems && !headerData.appDownload}]">
                            <div class="list-right-box list-box">
                              <a @click="ga4Event({nav_group:item.nav, method:'Big Title', nav_name:item.rightTitle, level: 'second', position: 'Right'})" v-if="item.rightTitle" class="font-body-md black95" :href="initLink(item.titleLink)">{{item.rightTitle}}</a>
                              <div v-for="link in item.rightItems" class="link-series">
                                <p v-if="link.title" class="font-note-sm black55">{{link.title}}</p>
                                <ul v-if="link.subItem && link.subItem.length > 0">
                                  <li v-for="subLink in link.subItem">
                                    <a @click="ga4Event({nav_group:item.nav, method:subLink.text, nav_name:link.title, level: 'second', position: 'Right'})" class="font-body-md black95" :href="initLink(subLink.textLink)" :target="subLink.newPage">{{subLink.text}}</a>
                                  </li>
                                </ul>
                              </div>
                              <div class="app-download" v-if="headerData.appDownload">
                                <a @click="ga4Event({nav_group:item.nav, method:'Get APP', nav_name:headerData.appDownload, level: 'second', position: 'Right'})" class="font-body-md black95" :href="initLink(headerData.appDownloadLink)" :target="headerData.appDownloadnewpage">
                                  {{headerData.appDownload}}

                                  <svg class="icon svg-icon arrow-icon" viewBox="0 0 20 20">
                                    <g clip-path="url(#icon-arrow-right-simple_clip0_430_12888)">
                                      <path d="M15.19 10.75H2v-1.5h13.19l-4.72-4.72 1.06-1.06L18.06 10l-6.53 6.53-1.06-1.06 4.72-4.72z"></path>
                                    </g>
                                    <defs>
                                      <clipPath id="icon-arrow-right-simple_clip0_430_12888">
                                        <path d="M0 0h20v20H0z"></path>
                                      </clipPath>
                                    </defs>
                                  </svg>
                                </a>
                              </div>
                            </div>
                          </div>
                        </div>
                      </nav>
                    </template>
                  </transition>
                  <!-- E PC二级导航 -->
                </li>
              </template>
            </ul>
          </div>
          <!-- E product nav -->
          <!-- S nav right -->
          <ul class="nav-right">
            <!-- S 搜索 -->
            <template v-if="!isDtbMode && Number(headerData.searchDisplay)">
              <transition name="xsMenu-fade">
                <li v-show="!isXsMenu" class="header-search" @mouseenter="getSearchInfo(1)" @mouseleave="getSearchInfo(0)">
                  <a @click="getSearchInfo(1, true)">
                    <svg class="svg-icon ico-header-search" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M16.6918 10.8959C16.6918 14.0969 14.0969 16.6918 10.8959 16.6918C7.69492 16.6918 5.1 14.0969 5.1 10.8959C5.1 7.69492 7.69492 5.1 10.8959 5.1C14.0969 5.1 16.6918 7.69492 16.6918 10.8959ZM15.5294 16.6608C14.2616 17.6812 12.6501 18.2918 10.8959 18.2918C6.81127 18.2918 3.5 14.9806 3.5 10.8959C3.5 6.81127 6.81127 3.5 10.8959 3.5C14.9806 3.5 18.2918 6.81127 18.2918 10.8959C18.2918 12.6501 17.6812 14.2616 16.6608 15.5294L20.5011 19.3697L19.3697 20.5011L15.5294 16.6608Z" fill="black"/>
                    </svg>
                  </a>
                  <!-- S 搜索弹窗 -->
                  <div class="search-card" :class="searchFocused ? 'active' : '' ">
                    <one-search ref="searchInput" v-if="searchInit" @submit="searchWord" focused-on @cancel="cancelXsSearch"></one-search>
                  </div>
                  <!-- E 搜索弹窗 -->
                </li>
              </transition>
            </template>
            <!-- E 搜索 -->
            <!-- S 购物车 -->
            <template v-if="!isDtbMode && Number(headerData.cartDisplay)">
              <transition name="xsMenu-fade">
                <li v-show="!isXsMenu" :class="['mini-cart', {'active': showMiniCart}]" @mouseenter="fetchCartInfo" @mouseleave="closeMiniCart" ref="shopBag">
                  <a :class="{'active': showMiniCart}" @click="clickMiniCart">
                    <svg class="ico svg-icon ico-cart show-number" viewBox="0 0 24 24" fill="none">
                      <path d="M3 4.75h1.825l.588 5.588.557 5.852A2 2 0 007.961 18h10.077a2 2 0 001.991-1.81l.762-8A2 2 0 0018.801 6H7.198c-.254 0-.497.047-.72.134L6.246 3.92a.75.75 0 00-.746-.67H3v1.5zm4.214 8.675l.032-.004-.34-3.233-.204-2.14a.5.5 0 01.497-.548H18.8a.5.5 0 01.498.547l-.762 8a.5.5 0 01-.498.453H7.961a.5.5 0 01-.497-.453l-.25-2.622z"></path>
                      <path d="M10 13.75a.75.75 0 01.75-.75h4.5a.75.75 0 010 1.5h-4.5a.75.75 0 01-.75-.75zM11 20a1 1 0 11-2 0 1 1 0 012 0zm6 0a1 1 0 11-2 0 1 1 0 012 0z"></path>
                    </svg>
                    <span class="red-dot font-note-xs" v-if="cart.itemNum && cart.itemNum > 0">{{cart.itemNum}}</span>
                  </a>
                  <!-- mini购物车-S  -->
                  <div :class="['cart-warper', {'active': showMiniCart}]">
                    <nav class="basket-info-nav">
                      <div class="basket-main">
                        <div class="cart-items-placeholder" v-if="cart.loading">
                          <one-spinner></one-spinner>
                          <div class="loading-text">{{searchData.loading}}</div>
                        </div>
                        <div v-else class="minicart-container" :class="{'empty-cart':!(cart.cartGoods && cart.cartGoods.length)}">
                          <ul class="cart-products" v-if="cart.cartGoods && cart.cartGoods.length && !cart.loading">
                            <li v-for="(item,index) in cart.cartGoods" class="products-link" :class="{'gift-item': item.buyType == 2}">
                              <a :href="item.urlPath">
                                <div class="cart-item-warper">
                                  <div class="cart-item-td td-image">
                                                                        <span class="image-placeholder">
                                                                            <img :src="clipImage(item.imageUrl, '160x160')" :srcset="clipImage(item.imageUrl, '160x160') + ' 1x,' + clipImage(item.imageUrl, '160x160') + ' 2x'" :alt="item.displayName" :srcset="item.imageUrl+ ' 1x,' + item.imageUrl+ ' 2x'">
                                                                        </span>
                                  </div>
                                  <div class="cart-item-info">
                                                                        <span class="main-item-warper">
                                                                            <div class="main-item">
                                                                                <!-- 商品名称 -->
                                                                                <div class="cart-item-name">
                                                                                    <!-- 商品名 -->
                                                                                    <div class="font-body-md">
                                                                                        <template v-if="item.attachment && item.attachment.productDisplayName">{{item.attachment.productDisplayName}}</template>
                                                                                        <template v-else>{{item.displayName}}</template>
                                                                                      <!-- 赠品标签 -->
                                                                                        <span class="tag-gift-tag font-note-xs" v-if="item.buyType == 2">{{searchData.gift}}</span>
                                                                                    </div>
                                                                                  <!-- 商品选型 -->
                                                                                    <template v-for="option in item.attachment.skuSelectOptions">
                                                                                        <span class="item-attr font-note-sm">{{option.optLabel}}</span>
                                                                                    </template>
                                                                                </div>
                                                                              <!-- 价格信息 -->
                                                                                <div class="item-price-info no-wrap font-note-sm">
                                                                                    <span class="now-price">{{formatCurrency(item.nowPrice)}}</span>
                                                                                    <span class="item-qty">x {{item.skuCount}}
                                          </span>
                                                                                </div>
                                                                            </div>
                                                                        </span>
                                    <span v-for="childItem in item.childItems" v-if="childItem.buyType == 2 || childItem.itemType == 4" class="cart-sku-gift-item">
                                                                            <div class="cart-item-name font-body-md">
                                                                                <span>{{childItem.displayName}}</span>
                                                                              <!-- 赠品标签 -->
                                                                                <span v-if="childItem.buyType == 2" class="tag-gift-tag font-note-xs">{{searchData.gift}}</span>
                                                                              <!-- 保险标签 -->
                                                                                <span v-else-if="childItem.itemType == 4" class="font-note-xs tag-gift-tag">{{searchData.insurance}}</span>
                                                                            </div>
                                                                            <span class="item-price-info no-wrap font-note-sm">
                                                                                <span v-if="childItem.buyType == 2" class="now-price">{{searchData.free}}</span>
                                                                                <span v-else-if="childItem.itemType == 4" class="now-price">{{formatCurrency(childItem.nowPrice)}}</span>
                                                                                <span class="item-qty">x {{childItem.skuCount}}</span>
                                                                            </span>
                                                                        </span>
                                    <!-- 以旧换新 -->
                                    <span v-if="item.evaluateOrder" class="cart-sku-gift-item" :class="item.evaluateOrder && item.evaluateOrder.evaluationIsExpired?'expired':''">
                                                                            <div class="cart-item-name font-body-md">
                                                                                <span>{{searchData.exchangeProgram}}</span>
                                                                                <span class="tag-gift-tag font-note-xs expired-tag">{{searchData.expired}}</span>
                                                                            </div>
                                                                            <span class="item-price-info no-wrap font-note-sm">
                                                                                <span class="now-price">-{{formatCurrency(item.evaluateOrder.evaluateTotalPrice)}}</span>
                                                                            </span>
                                                                        </span>
                                  </div>
                                </div>
                              </a>
                            </li>
                          </ul>
                          <!-- 购物车为空 -->
                          <div class="cart-no-products" v-if="cart.cartGoods && !cart.cartGoods.length && !cart.loading">
                            <div class="text-container font-body-lg">
                              <p v-if="!cart.showError">{{searchData.emptyCart}}
                              </p>
                              <p v-if="cart.showError">{{searchData.oopsText}}
                              </p>
                            </div>
                            <div class="icon-container">
                              <svg class="ico svg-icon ico-shop-bag" viewBox="0 0 120 120" fill="none">
                                <path d="M35.25 46.517a1.03 1.03 0 011.022-1.165h57.517a1.03 1.03 0 011.022 1.165l-4.148 31.54a1.03 1.03 0 01-1.021.897H40.419a1.03 1.03 0 01-1.022-.896L35.25 46.517z" stroke="#000" stroke-width="1.237"></path>
                                <path d="M15 36.358h11.872a3.092 3.092 0 013.066 2.689l6.726 51.065a3.092 3.092 0 003.065 2.688h48.04" stroke="#000" stroke-width="1.237"></path>
                                <ellipse cx="77.235" cy="96.354" rx="2.826" ry="2.955" stroke="#000" stroke-width="1.237"></ellipse>
                                <ellipse cx="52.055" cy="96.354" rx="2.826" ry="2.955" stroke="#000" stroke-width="1.237"></ellipse>
                                <path d="M39.4 76.6h-4.803m-3.354-26.214h4.625" stroke="#000" stroke-width="1.237"></path>
                                <path d="M29.701 40.413h-13.47a1.03 1.03 0 01-1.03-1.031v-3.594M36.2 89h50.37c.569 0 1.03.462 1.03 1.031v3.37M35.868 50.334h58.496" stroke="#000" stroke-width=".618"></path>
                                <path opacity=".45" d="M55.35 53.317l-11.774 10.19L56.03 77.895l11.773-10.19L55.35 53.316zm19.691 3.034l9.132 5.51-9.361 16.073-9.14-5.276 9.369-16.307zm-7.827 7.195a4.625 4.625 0 100-9.25 4.625 4.625 0 000 9.25z" stroke="#F50514" stroke-width=".412" stroke-miterlimit="10" stroke-linecap="round" stroke-dasharray="0.82 0.82"></path>
                                <circle cx="65.11" cy="31" r="11" stroke="#F50514" stroke-width=".412" stroke-linecap="round" stroke-dasharray="1.24 1.24"></circle>
                                <path d="M65.621 29.477c.535-.366 1.037-.76 1.037-1.554 0-.633-.308-1.613-1.619-1.613-1.31 0-1.555.996-1.634 1.534h-1.33c0-1.091.682-2.705 2.93-2.705 2.009 0 2.98 1.282 2.98 2.8 0 1.392-.808 2.01-1.36 2.39-.777.538-1.343.934-1.343 1.994v.98h-1.327V32.29c0-1.643.955-2.322 1.666-2.813zm-1.86 6.018c0-.252.101-.493.284-.671a.983.983 0 011.373 0c.183.178.285.42.285.671a.939.939 0 01-.285.672.983.983 0 01-.687.277.983.983 0 01-.686-.278.939.939 0 01-.285-.67z" fill="#F50514"></path>
                              </svg>
                            </div>
                            <div class="cart-free-limit free-text font-body-md">
                                                            <span>
                                                                {{searchData.youAre}}
                                <span class="shipping-fee" v-if="cart.cartGoods && cart.cartGoods.length">{{freeShippingPrice}}</span>
                                                                <span v-else class="shipping-fee">$100</span>
                                                                {{searchData.freeShipping}}

                                                            </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="cart-info" v-if="!cart.loading && !isEmptyCart">
                        <div class="cart-link" v-if="!cart.showError">
                          <div class="text">
                            <div class="total-price font-body-lg">
                              <span class="label">{{searchData.total}}</span>
                              <span class="value">{{formatCurrency(cart.totalPrice)}}</span>
                            </div>
                            <div class="gifts-tips" v-if="cart.giftsTips">{{searchData.freeGift}}</div>
                          </div>
                          <div class="cart-free-limit font-note-sm" v-if="!cart.loading">
                            <span v-if="cart.freeShopping">{{searchData.getGift}}</span>
                            <span v-else>
                                                            {{searchData.youAre}}
                              <span class="shipping-fee" v-if="cart.cartGoods && cart.cartGoods.length">{{freeShippingPrice}}</span>
                                                            <span v-else class="shipping-fee">$100</span>
                                                            {{searchData.freeShipping}}

                                                        </span>
                          </div>
                          <div class="buttons">
                            <div class="item">
                              <a @click="ga4Event({button_name:searchData.checkCart, function_type: 'Shopping cart'}, 'function_entry')" class="new-button new-button--secondary btn-text full-width is-block" :href="initLink('/jcart?from=mini_cart')">{{searchData.checkCart}}</a>
                            </div>
                            <div class="item">
                              <a @click="ga4Event({button_name:searchData.checkout, function_type: 'Shopping cart'}, 'function_entry')" class="new-button new-button--primary btn-text full-width is-block" href="https://www.oneplus.com/us/checkout/payment" @click.prevent="checkGift">{{searchData.checkout}}</a>
                            </div>
                          </div>
                        </div>
                      </div>
                    </nav>
                  </div>
                  <!-- mini购物车-E -->
                </li>
              </transition>
            </template>
            <!-- E 购物车 -->
            <!-- S 移动端汉堡按钮 -->
            <li class="menu-icon">
              <a @click="showMenu" :class="{'show-menu': isXsMenu}">
                <i class="icon-show-menu"></i>
              </a>
            </li>
            <!-- E 移动端汉堡按钮 -->
            <!-- S 个人信息 -->
            <li v-if="Number(headerData.accountDisplay)" class="header-info" @mouseenter="showUserInfo(1)" @mouseleave="showUserInfo(0)">
              <a @click="goUserIconLogin({button_name:'Account center', function_type:'entry'}, 'function_entry')">
                <img v-if="user.signedIn && user.avatar" class="pc-user-img" :src="user.avatar" alt="Avatar">
                <svg v-else class="ico svg-icon ico-user" viewBox="0 0 24 24">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M4.5 12a7.5 7.5 0 1015 0 7.5 7.5 0 00-15 0zM3 12a9 9 0 1018 0 9 9 0 00-18 0z"></path>
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M12 12.535a2 2 0 100-4 2 2 0 000 4zm0 1.5a3.5 3.5 0 100-7 3.5 3.5 0 000 7z"></path>
                  <path d="M18.063 18.651a6.502 6.502 0 00-12.126 0 9.058 9.058 0 01-1.105-1.207A8 8 0 0112 13a8 8 0 017.168 4.443c-.331.436-.701.84-1.105 1.208z"></path>
                </svg>
              </a>
              <div class="user-info-pc">
                <div class="user-info-box">
                  <!-- dtb模式下 小标题 -->
                  <div class="user-info-dtb-title font-note-sm" v-if="isDtbMode">{{searchData.onePlusBusiness}}
                  </div>
                  <div class="sigin-box">
                    <a :class="['user-img', {'user-img-dtb': isDtbMode || user.isMembership}]" @click="goUserIconLogin({function_type: 'entry', button_name: 'Account center'}, 'function_entry')">
                      <img v-if="user.signedIn && user.avatar" :src="user.avatar" alt="Avatar">
                      <img v-else class="op-lazyload" data-src-2x="https://image01.oneplus.net/shop/202207/12/1-M00-3D-6B-rB8LB2LM696ARA9oAAACNk-7O5M305.svg" alt="Avatar">
                      <span v-if="isDtbMode" class="user-dtb"></span>
                      <span v-else-if="user.isMembership" class="user-membership">
                                                <svg class="ico ico-membership" fill="none" viewBox="0 0 16 16">
                                                    <path d="M0 8a8 8 0 1116 0A8 8 0 110 8z" fill="#fff"></path>
                                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M8 2a6 6 0 100 12A6 6 0 008 2zm3.292 9.382a4.724 4.724 0 01-6.674 0 4.724 4.724 0 010-6.674 4.724 4.724 0 016.674 0 4.724 4.724 0 010 6.674z" fill="#F50514"></path>
                                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M8 4.657a3.342 3.342 0 10-.001 6.685 3.342 3.342 0 000-6.685zm-.025 5.406a2.04 2.04 0 01-2.04-2.04 2.04 2.04 0 012.04-2.038 2.04 2.04 0 012.039 2.039 2.041 2.041 0 01-2.04 2.039z" fill="#F50514"></path>
                                                </svg>
                                            </span>
                    </a>
                    <div class="user-sigin">
                      <p class="font-body-md black95">{{user.signedIn ? user.name : searchData.welcome}}</p>
                      <p v-if="!user.signedIn" class="sigin font-body-md">
                        <a href="javascript:void(0)" @click="goUserIconLogin({module:searchData.signUp,button_name: 'Entry'},'account_setting')">{{searchData.signUp}}</a>
                        <i></i>
                        <a href="javascript:void(0)" @click="goUserIconLogin({module:searchData.signIn,button_name: 'Entry'},'account_setting')">{{searchData.signIn}}</a>
                      </p>
                      <p v-else class="font-body-md sigin">
                        <a href="javascript:void(0)" @click="userSignOut({module:searchData.signOut,button_name: 'Entry'},'account_setting')">{{searchData.signOut}}</a>
                      </p>
                    </div>
                  </div>
                  <div class="user-menu">
                    <ul>
                      <li>
                        <a @click="ga4Event({function_type:'Account center', button_name: searchData.order}, 'function_entry')" :href="user.signedIn ? initLink('/sales/order/history?from=head') : initLink('/check-order?from=head')">
                          <div class="img-box">
                            <img class="op-lazyload" data-src-2x="https://image01-in.oneplus.net/shop/202206/14/1-M00-3D-46-rB8LB2KoUleAUUNsAAABv1Ho6hY193.svg" alt="">
                            <span class="font-body-md black95">{{searchData.order}}</span>
                          </div>
                          <svg class="ico svg-icon" viewBox="0 0 20 20">
                            <path clip-rule="evenodd" d="M13 10.41l-5.25 5.506-.861-.905L11.667 10 6.889 4.988l.861-.905L13 9.589c.22.226.22.594 0 .821z"></path>
                          </svg>
                        </a>
                      </li>
                      <li>
                        <a @click="ga4Event({function_type:'Account center', button_name: searchData.account}, 'function_entry')" :href="initLink('/customer/info?from=head')">
                          <div class="img-box">
                            <img class="op-lazyload" data-src-2x="https://image01-in.oneplus.net/shop/202206/09/1-M00-3C-FD-rB8LB2KhoROAB5tdAAAEVeKlMcI807.svg" alt="">
                            <span class="font-body-md black95">{{searchData.account}}</span>
                          </div>
                          <svg class="ico svg-icon" viewBox="0 0 20 20">
                            <path clip-rule="evenodd" d="M13 10.41l-5.25 5.506-.861-.905L11.667 10 6.889 4.988l.861-.905L13 9.589c.22.226.22.594 0 .821z"></path>
                          </svg>
                        </a>
                      </li>
                      <li class="nav-badge nav-badge-mo" v-if="headerData.achievementDisplay">
                        <a @click="ga4Event({function_type:'Account center', button_name: searchData.achievement}, 'function_entry')" class="user-menu-item font-body-md" :href="initLink('/rcc/badge?from=head')">
                          <div class="img-box">
                            <img class="op-lazyload" data-src-2x="https://image01.oneplus.net/shop/202311/15/1-M00-48-89-CkvTlmVUbrqAHXt3AAAFepATGcY166.svg" alt="">
                            <span class="font-body-md black95">{{searchData.achievement}}</span>
                          </div>
                          <div class="nav-badge-right">
                            <div class="badge-icon">
                              <img class="op-lazyload" v-for="item in badgeList" :data-src-2x="item" alt="">
                            </div>
                            <svg class="ico svg-icon" viewBox="0 0 20 20">
                              <path clip-rule="evenodd" d="M13 10.41l-5.25 5.506-.861-.905L11.667 10 6.889 4.988l.861-.905L13 9.589c.22.226.22.594 0 .821z"></path>
                            </svg>
                          </div>
                        </a>
                      </li>
                      <template v-for="data in headerData.membersItems">
                        <li @click="ga4Event({function_type:'Account center', button_name: data.title}, 'function_entry')" v-if="(Number(data.isDtb) && isDtbMode) || (!isDtbMode && !Number(data.isDtb))">
                          <a :href="initLink(data.link)" :target="data.newPage">
                            <div class="img-box">
                              <img class="op-lazyload" :data-src-2x="data.icon || 'https://image01.oneplus.net/shop/202206/17/1-M00-3D-5A-rB8bwmKsMNiAJlS1AAADtHsnzMM635.svg'" alt="">
                              <span class="font-body-md black95">{{data.title}}</span>
                            </div>
                            <svg class="ico svg-icon" viewBox="0 0 20 20">
                              <path clip-rule="evenodd" d="M13 10.41l-5.25 5.506-.861-.905L11.667 10 6.889 4.988l.861-.905L13 9.589c.22.226.22.594 0 .821z"></path>
                            </svg>
                          </a>
                        </li>
                      </template>
                    </ul>
                  </div>
                  <!-- dtb 切换模式 -->
                  <div v-if="hasDtbAuth">
                    <div class="dtb-switch-mode font-note-sm flex" @click="switchDtbMode">{{isDtbMode ? searchData.personalMode : searchData.businessMode}}</div>
                  </div>
                </div>
              </div>
            </li>
            <!-- E 个人信息 -->
          </ul>
          <!-- E nav right -->
        </div>
        <!-- 移动端菜单栏 -->
        <transition name="xsNav-fade">
          <div v-show="isXsMenu" :class="['xs-nav-list', {'xs-nav-show': isXsMenu}]">
            <div class="xs-nav-box">
              <transition name="xsFirstNav-fade">
                <div v-show="!productMenuFlag">
                  <!-- 产品列表 -->
                  <div class="product-list" v-if="xsMenuProductData && xsMenuProductData.length > 0">
                    <ul>
                      <template v-for="item in xsMenuProductData">
                        <li :class="['navList', item.navStyle]" v-if="(isDtbMode && Number(item.isDtb)) || (!isDtbMode && !Number(item.isDtb)) || item.navType == 'commonNav'" @click="goProductMenu(item)">
                          <div class="title-box">
                            <div class="img-box">
                              <img class="op-lazyload" :data-src-2x="item.navImg" alt="">
                            </div>
                            <span class="font-body-md black95">{{item.nav}}</span>
                          </div>
                          <svg class="ico svg-icon" viewBox="0 0 20 20">
                            <path clip-rule="evenodd" d="M13 10.41l-5.25 5.506-.861-.905L11.667 10 6.889 4.988l.861-.905L13 9.589c.22.226.22.594 0 .821z"></path>
                          </svg>
                        </li>
                      </template>
                    </ul>
                  </div>
                  <!-- 纯外链列表 -->
                  <div class="product-link product-list">
                    <ul>
                      <template v-for="item in xsMenuLinkData">
                        <li :class="['navList', item.navStyle]" v-if="(isDtbMode && Number(item.isDtb)) || (!isDtbMode && !Number(item.isDtb)) || item.navType == 'commonNav'">
                          <a :href="initLink(item.navItemLink)" @click="ga4Event({level: 'first',nav_group: 'none',nav_name: item.navItem})">
                            <div class="title-box">
                              <span class="font-body-md black95">{{item.nav}}</span>
                            </div>
                            <svg class="ico svg-icon" viewBox="0 0 20 20">
                              <path clip-rule="evenodd" d="M13 10.41l-5.25 5.506-.861-.905L11.667 10 6.889 4.988l.861-.905L13 9.589c.22.226.22.594 0 .821z"></path>
                            </svg>
                          </a>
                        </li>
                      </template>
                    </ul>
                  </div>
                  <!-- 个人信息栏 -->
                  <template v-if="Number(headerData.accountDisplay)">
                    <div class="user-info">
                      <div class="user-info-box">
                        <!-- dtb模式下 小标题 -->
                        <div class="user-info-dtb-title font-note-sm" v-if="isDtbMode">{{searchData.onePlusBusiness}}
                        </div>
                        <div class="sigin-box">
                          <a :class="['user-img', {'user-img-dtb': isDtbMode || user.isMembership}]" @click="goUserIconLogin({function_type: 'entry', button_name: 'Account center'}, 'function_entry')">
                            <img v-if="user.signedIn && user.avatar" class="op-lazyload" :data-src-2x="user.avatar" alt="Avatar">
                            <img v-else class="op-lazyload" data-src-2x="https://image01.oneplus.net/shop/202207/12/1-M00-3D-6B-rB8LB2LM696ARA9oAAACNk-7O5M305.svg" alt="Avatar">
                            <span v-if="isDtbMode" class="user-dtb"></span>
                            <span v-else-if="user.isMembership" class="user-membership">
                                                            <svg class="ico ico-membership" fill="none" viewBox="0 0 16 16">
                                                                <path d="M0 8a8 8 0 1116 0A8 8 0 110 8z" fill="#fff"></path>
                                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M8 2a6 6 0 100 12A6 6 0 008 2zm3.292 9.382a4.724 4.724 0 01-6.674 0 4.724 4.724 0 010-6.674 4.724 4.724 0 016.674 0 4.724 4.724 0 010 6.674z" fill="#F50514"></path>
                                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M8 4.657a3.342 3.342 0 10-.001 6.685 3.342 3.342 0 000-6.685zm-.025 5.406a2.04 2.04 0 01-2.04-2.04 2.04 2.04 0 012.04-2.038 2.04 2.04 0 012.039 2.039 2.041 2.041 0 01-2.04 2.039z" fill="#F50514"></path>
                                                            </svg>
                                                        </span>
                          </a>
                          <div class="user-sigin">
                            <p class="font-body-md black95">{{user.signedIn ? user.name : searchData.welcome}}</p>
                            <p v-if="!user.signedIn" class="sigin font-body-md">
                              <a href="javascript:void(0)" @click="goUserIconLogin({module:searchData.signUp,button_name: 'Entry'},'account_setting')">{{searchData.signUp}}</a>
                              <i></i>
                              <a href="javascript:void(0)" @click="goUserIconLogin({module:searchData.signIn,button_name: 'Entry'},'account_setting')">{{searchData.signIn}}</a>
                            </p>
                            <p v-else class="font-body-md sigin">
                              <a href="javascript:void(0)" @click="userSignOut({module:searchData.signOut,button_name: 'Entry'},'account_setting')">{{searchData.signOut}}</a>
                            </p>
                          </div>
                        </div>
                        <div class="user-menu">
                          <ul>
                            <li>
                              <a @click="ga4Event({function_type:'Account center', button_name: searchData.order}, 'function_entry')" :href="user.signedIn ? initLink('/sales/order/history?from=head') : initLink('/check-order?from=head')">
                                <div class="img-box">
                                  <img class="op-lazyload" data-src-2x="https://image01-in.oneplus.net/shop/202206/14/1-M00-3D-46-rB8LB2KoUleAUUNsAAABv1Ho6hY193.svg" alt="">
                                  <span class="font-body-md black95">{{searchData.order}}</span>
                                </div>
                                <svg class="ico svg-icon" viewBox="0 0 20 20">
                                  <path clip-rule="evenodd" d="M13 10.41l-5.25 5.506-.861-.905L11.667 10 6.889 4.988l.861-.905L13 9.589c.22.226.22.594 0 .821z"></path>
                                </svg>
                              </a>
                            </li>
                            <li>
                              <a @click="ga4Event({function_type:'Account center', button_name: searchData.account}, 'function_entry')" :href="initLink('/customer/info?from=head')">
                                <div class="img-box">
                                  <img class="op-lazyload" data-src-2x="https://image01-in.oneplus.net/shop/202206/09/1-M00-3C-FD-rB8LB2KhoROAB5tdAAAEVeKlMcI807.svg" alt="">
                                  <span class="font-body-md black95">{{searchData.account}}</span>
                                </div>
                                <svg class="ico svg-icon" viewBox="0 0 20 20">
                                  <path clip-rule="evenodd" d="M13 10.41l-5.25 5.506-.861-.905L11.667 10 6.889 4.988l.861-.905L13 9.589c.22.226.22.594 0 .821z"></path>
                                </svg>
                              </a>
                            </li>
                            <li class="nav-badge nav-badge-mo" v-if="Number(headerData.achievementDisplay)">
                              <a @click="ga4Event({function_type:'Account center', button_name: searchData.achievement}, 'function_entry')" class="user-menu-item font-body-md" :href="initLink('/rcc/badge?from=head')">
                                <div class="img-box">
                                  <img class="op-lazyload" data-src-2x="https://image01.oneplus.net/shop/202311/15/1-M00-48-89-CkvTlmVUbrqAHXt3AAAFepATGcY166.svg" alt="">
                                  <span class="font-body-md black95">{{searchData.achievement}}</span>
                                </div>
                                <div class="nav-badge-right">
                                  <div class="badge-icon">
                                    <img class="op-lazyload" v-for="item in badgeList" :data-src-2x="item" alt="">
                                  </div>
                                  <svg class="ico svg-icon" viewBox="0 0 20 20">
                                    <path clip-rule="evenodd" d="M13 10.41l-5.25 5.506-.861-.905L11.667 10 6.889 4.988l.861-.905L13 9.589c.22.226.22.594 0 .821z"></path>
                                  </svg>
                                </div>
                              </a>
                            </li>
                            <template v-for="data in headerData.membersItems">
                              <li v-if="(Number(data.isDtb) && isDtbMode) || (!isDtbMode && !Number(data.isDtb))">
                                <a @click="ga4Event({function_type:'Account center', button_name: data.title}, 'function_entry')" :href="initLink(data.link)" :target="data.newPage">
                                  <div class="img-box">
                                    <img class="op-lazyload" :data-src-2x="data.icon || 'https://image01.oneplus.net/shop/202206/17/1-M00-3D-5A-rB8bwmKsMNiAJlS1AAADtHsnzMM635.svg'" alt="">
                                    <span class="font-body-md black95">{{data.title}}</span>
                                  </div>
                                  <svg class="ico svg-icon" viewBox="0 0 20 20">
                                    <path clip-rule="evenodd" d="M13 10.41l-5.25 5.506-.861-.905L11.667 10 6.889 4.988l.861-.905L13 9.589c.22.226.22.594 0 .821z"></path>
                                  </svg>
                                </a>
                              </li>
                            </template>
                          </ul>
                        </div>
                        <!-- dtb 切换模式 -->
                        <div v-if="hasDtbAuth">
                          <div class="dtb-switch-mode font-note-sm flex" @click="switchDtbMode">{{isDtbMode ? searchData.personalMode : searchData.businessMode}}</div>
                        </div>
                      </div>
                    </div>
                  </template>
                </div>
              </transition>
              <!-- S 二级菜单 -->
              <transition name="xsSecNav-fade">
                <div v-if="productMenuFlag" class="product-info">
                  <div class="product-info-top">
                    <a v-if="xsMenuData.leftTitle" :href="initLink(xsMenuData.secondTitleLink)" class="product-info-title" @click="ga4Event({nav_group:xsMenuData.nav, method:'Big Title', nav_name:xsMenuData.leftTitle, level: 'second', position: 'Left'})">
                      <span class="font-body-xl black95">{{xsMenuData.leftTitle}}</span>
                      <svg class="icon svg-icon arrow-icon" viewBox="0 0 20 20">
                        <g clip-path="url(#icon-arrow-right-simple_clip0_430_12888)">
                          <path d="M15.19 10.75H2v-1.5h13.19l-4.72-4.72 1.06-1.06L18.06 10l-6.53 6.53-1.06-1.06 4.72-4.72z"></path>
                        </g>
                        <defs>
                          <clipPath id="icon-arrow-right-simple_clip0_430_12888">
                            <path d="M0 0h20v20H0z"></path>
                          </clipPath>
                        </defs>
                      </svg>
                    </a>
                    <div v-for="list in xsMenuData.leftItems" class="product-info-list">
                      <a v-if="list.title" :href="initLink(list.titleLink)" :class="[list.subItem && list.subItem.length > 0 ? 'font-note-sm black55' : 'emptyList font-body-md black95', 'list-title']" @click="ga4Event({nav_group:xsMenuData.nav, method:list.title, nav_name:xsMenuData.leftTitle, level: 'second', position: 'Left'})">{{list.title}}</a>
                      <ul v-if="list.subItem && list.subItem.length > 0">
                        <li v-for="product in list.subItem">
                          <a @click="ga4Event({nav_group:xsMenuData.nav, method:list.title, nav_name:product.name, level: 'second', position: 'Left'})" :href="initLink(product.link)" :target="product.newPage">
                            <div class="list-img-box">
                              <img :src="product.img" alt="">
                            </div>
                            <p class="product-info-name font-body-md black95">{{product.name}}</p>
                            <p v-if="product.tag" class="product-info-tag font-note-xs">{{product.tag}}</p>
                          </a>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div class="product-info-bottom" v-if="(xsMenuData.rightItems && xsMenuData.rightItems.length> 0) || headerData.appDownload">
                    <a v-if="xsMenuData.rightTitle" :href="initLink(xsMenuData.titleLink)" class="list-bottom-title font-body-md black95" @click="ga4Event({nav_group:xsMenuData.nav, method:'Big Title', nav_name:xsMenuData.title, level: 'second', position: 'Right'})">{{xsMenuData.rightTitle}}</a>
                    <div v-for="link in xsMenuData.rightItems" class="product-info-link">
                      <div v-if="link.title" class="product-link-title font-note-sm black55">{{link.title}}</div>
                      <ul>
                        <li v-for="subLink in link.subItem">
                          <a @click="ga4Event({nav_group:xsMenuData.nav, method:subLink.text, nav_name:link.title, level: 'second', position: 'Right'})" class="font-body-md black95" :href="initLink(subLink.textLink)" :target="subLink.newPage">{{subLink.text}}</a>
                        </li>
                      </ul>
                    </div>
                    <div class="app-download" v-if="headerData.appDownload">
                      <a class="font-body-md black95" :href="initLink(headerData.appDownloadLink)" :target="headerData.appDownloadnewpage">
                        {{headerData.appDownload}}

                        <svg class="icon svg-icon arrow-icon" viewBox="0 0 20 20">
                          <g clip-path="url(#icon-arrow-right-simple_clip0_430_12888)">
                            <path d="M15.19 10.75H2v-1.5h13.19l-4.72-4.72 1.06-1.06L18.06 10l-6.53 6.53-1.06-1.06 4.72-4.72z"></path>
                          </g>
                          <defs>
                            <clipPath id="icon-arrow-right-simple_clip0_430_12888">
                              <path d="M0 0h20v20H0z"></path>
                            </clipPath>
                          </defs>
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>
              </transition>
              <!-- E 二级菜单 -->
            </div>
          </div>
        </transition>
        <!-- 移动端菜单栏 -->
      </div>
      <!-- mask overlay -->
      <transition name="mask-fade">
        <div v-show="showOverlay" class="overlay" @click="clickOverlay"></div>
      </transition>
    </header>
  </div>
</div>
<script type="application/json" id="data-search-translation">
  {
    "cancel": "Cancel",
    "loading": "Loading...",
    "hot": "Hot",
    "noMatch": "Sorry, no matches were found.",
    "popular": "Popular",
    "search": "Search",
    "history": "Search history",
    "gift": "Gift",
    "insurance": "Insurance",
    "free": "Free",
    "exchangeProgram": "Exchange Program",
    "expired": "Expired",
    "emptyCart": "Your cart is empty",
    "oopsText": "Oops, something went wrong in your shopping cart. See what needs to be adjusted.",
    "youAre": "You are",
    "freeShipping": "away from free shipping.",
    "total": "Total",
    "freeGift": "Please remember to add free gift on the cart.",
    "getGift": "Congratulations! You get free shipping",
    "checkCart": "Check my cart",
    "checkout": "Checkout",
    "onePlusBusiness": "OnePlus Business",
    "welcome": "Welcome to OnePlus",
    "signUp": "Sign up",
    "signIn": "Sign in",
    "signOut": "Sign out",
    "order": "Orders",
    "account": "Account",
    "achievement": "Achievement",
    "personalMode": "Switch to Personal Mode",
    "businessMode": "Switch to Business Mode"
  }</script>
<script type="application/json" id="header-data">
  {
    "appDownloadnewpage": "_self",
    "productItems": [
      {
        "0": "-",
        "rightItems": [
          {
            "subItem": [
              {
                "newPage": "_blank",
                "text": "Invite Friends",
                "textLink": "/invite-friends?activityId=21"
              },
              {
                "newPage": "_blank",
                "text": "Trade-in \u0026 Upgrade",
                "textLink": "/trade-in"
              },
              {
                "newPage": "_blank",
                "text": "Students Discounts",
                "textLink": "/discount-program?verify\u003deducation_program"
              },
              {
                "newPage": "_blank",
                "text": "Employee Discounts",
                "textLink": "/employee-groups?verify\u003demployee"
              },
              {
                "newPage": "_blank",
                "text": "Membership Benefits",
                "textLink": "/rcc#/membership"
              },
              {
                "newPage": "_blank",
                "text": "Exclusive savings in OnePlus Store App",
                "textLink": "https://www.oneplus.com/us/deep-link?app_page\u003doneplusstore%3A%2F%2Foneplus%2Fapp%3Furi%3Dcom.oneplus.mall.action.main%26tabIndex%3D0"
              }
            ],
            "title": "Offers"
          }
        ],
        "navImg": "/content/dam/oasis/page/2024/nav/common/store.png",
        "nav": "Store",
        "isDtb": "0",
        "leftTitle": "Go to Official Store",
        "navType": "commonNav",
        "secondTitleLink": "/store",
        "leftItems": [
          {
            "subItem": [
              {
                "img": "/content/dam/oasis/page/2024/nav/in/waffle-green.png",
                "newPage": "_blank",
                "link": "/store/phone",
                "name": "Phone"
              },
              {
                "img": "/content/dam/oasis/page/2023/nav/pad-nav.png",
                "newPage": "_blank",
                "link": "/store/tablet",
                "name": "Tablet"
              },
              {"img": "/content/dam/oasis/page/2024/nav/common/almond-silver.png",
                "newPage": "_blank",
                "link": "/store/wearables",
                "name": "Wearables"
              },
              {
                "img": "/content/dam/oasis/page/2024/nav/in/eular.png",
                "newPage": "_blank",
                "link": "/store/audio",
                "name": "Audio"
              },
              {
                "img": "/content/dam/oasis/page/2024/nav/us/nav-more-products-NA.png",
                "newPage": "_blank",
                "link": "/store/power-cables",
                "name": "Accessories"
              },
              {
                "img": "/content/dam/oasis/page/2024/nav/common/bundles.png",
                "newPage": "_blank",
                "link": "/store/all",
                "name": "More Products"
              }
            ]
          }
        ],
        "navItemLink": "/store"
      },
      {
        "0": "-",
        "rightItems": [
          {
            "subItem": [
              {
                "newPage": "_blank",
                "text": "Invite Friends",
                "textLink": "/invite-friends?activityId=21"
              },
              {
                "newPage": "_blank",
                "text": "Trade-in \u0026 Upgrade",
                "textLink": "/trade-in"
              },
              {
                "newPage": "_blank",
                "text": "Students Discounts",
                "textLink": "/discount-program?verify\u003deducation_program"
              },
              {
                "newPage": "_blank",
                "text": "Employee Discounts",
                "textLink": "/employee-groups?verify\u003demployee"
              },
              {
                "newPage": "_blank",
                "text": "Membership Benefits",
                "textLink": "/rcc#/membership"
              },
              {
                "newPage": "_blank",
                "text": "Exclusive savings in OnePlus Store App",
                "textLink": "https://www.oneplus.com/us/deep-link?app_page\u003doneplusstore%3A%2F%2Foneplus%2Fapp%3Furi%3Dcom.oneplus.mall.action.main%26tabIndex%3D0"
              }
            ],
            "title": "Offers"
          }
        ],
        "navImg": "/content/dam/oasis/page/2024/nav/common/phone.png",
        "nav": "Phone",
        "isDtb": "0",
        "leftTitle": "Explore All Phone",
        "navType": "commonNav",
        "secondTitleLink": "/store/phone",
        "leftItems": [
          {
            "subItem": [
              {
                "img": "/content/dam/oneplus/2024/nav/13-Black.png",
                "newPage": "_blank",
                "link": "/oneplus-13",
                "name": "OnePlus 13",
                "tag": "New"
              },
              {
                "img": "/content/dam/oneplus/2024/nav/13r-black.png",
                "newPage": "_blank",
                "link": "/oneplus-13r",
                "name": "OnePlus 13R",
                "tag": "New"
              },
              {
                "img": "/content/dam/oasis/page/2024/nav/in/waffle-green.png",
                "newPage": "_blank",
                "link": "/oneplus-12",
                "name": "OnePlus 12"
              },
              {
                "img": "/content/dam/oasis/page/2024/nav/in/aston-blue.png",
                "newPage": "_blank",
                "link": "/oneplus-12r",
                "name": "OnePlus 12R"
              },
              {
                "img": "/content/dam/oasis/page/2023/nav/green-pc2.png",
                "newPage": "_blank",
                "link": "/oneplus-open",
                "name": "OnePlus Open"
              },
              {
                "img": "/content/dam/oasis/page/2023/nav/nav-larry-black.png",
                "newPage": "_blank",
                "link": "/oneplus-n30-5g",
                "name": "OnePlus Nord N30 5G"
              }
            ]
          }
        ],
        "navItemLink": "/store/phone",
        "navNewPage": "_self",
        "rightTitle": "Compare Phones",
        "titleLink": "/phone/compare"
      },
      {
        "0": "-",
        "rightItems": [
          {
            "subItem": [
              {
                "newPage": "_blank",
                "text": "Invite Friends",
                "textLink": "/invite-friends?activityId=21"
              },
              {
                "newPage": "_blank",
                "text": "Trade-in \u0026 Upgrade",
                "textLink": "/trade-in"
              },
              {
                "newPage": "_blank",
                "text": "Students Discounts",
                "textLink": "/discount-program?verify\u003deducation_program"
              },
              {
                "newPage": "_blank",
                "text": "Employee Discounts",
                "textLink": "/employee-groups?verify\u003demployee"
              },
              {
                "newPage": "_blank",
                "text": "Membership Benefits",
                "textLink": "/rcc#/membership"
              },
              {
                "newPage": "_blank",
                "text": "Exclusive savings in OnePlus Store App",
                "textLink": "https://www.oneplus.com/us/deep-link?app_page\u003doneplusstore%3A%2F%2Foneplus%2Fapp%3Furi%3Dcom.oneplus.mall.action.main%26tabIndex%3D0"
              }
            ],
            "title": "Offers"
          }
        ],
        "navImg": "/content/dam/oasis/page/2024/nav/common/tablet.png",
        "nav": "Tablet",
        "isDtb": "0",
        "leftTitle": "Explore All Tablet",
        "navType": "commonNav",
        "secondTitleLink": "/store/tablet",
        "leftItems": [
          {
            "subItem": [
              {
                "img": "/content/dam/oasis/page/2024/nav/eu/pad2-nav-new.png",
                "newPage": "_blank",
                "link": "/buy-oneplus-pad-2",
                "name": "OnePlus Pad 2"
              },
              {
                "img": "/content/dam/oasis/page/2023/nav/pad-nav.png",
                "newPage": "_blank",
                "link": "/buy-oneplus-pad",
                "name": "OnePlus Pad"
              },
              {
                "img": "/content/dam/oasis/page/2023/nav/shelltopnav.png",
                "newPage": "_blank",
                "link": "/store/tablet",
                "name": "Tablet Accessories"
              }
            ]
          }
        ],
        "navItemLink": "/store/tablet"
      },
      {
        "0": "-",
        "rightItems": [
          {
            "subItem": [
              {"newPage": "_blank",
                "text": "Invite Friends",
                "textLink": "/invite-friends?activityId=21"
              },
              {
                "newPage": "_blank",
                "text": "Trade-in \u0026 Upgrade",
                "textLink": "/trade-in"
              },
              {
                "newPage": "_blank",
                "text": "Students Discounts",
                "textLink": "/discount-program?verify\u003deducation_program"
              },
              {
                "newPage": "_blank",
                "text": "Employee Discounts",
                "textLink": "/employee-groups?verify\u003demployee"
              },
              {
                "newPage": "_blank",
                "text": "Membership Benefits",
                "textLink": "/rcc#/membership"
              },
              {
                "newPage": "_blank",
                "text": "Exclusive savings in OnePlus Store App",
                "textLink": "https://www.oneplus.com/us/deep-link?app_page\u003doneplusstore%3A%2F%2Foneplus%2Fapp%3Furi%3Dcom.oneplus.mall.action.main%26tabIndex%3D0"
              }
            ],
            "title": "Offers"
          }
        ],
        "navImg": "/content/dam/oasis/page/2024/nav/common/wearable.png",
        "nav": "Wearables",
        "isDtb": "0",
        "leftTitle": "Explore All  Wearables",
        "navType": "commonNav",
        "secondTitleLink": "/store/wearables",
        "leftItems": [
          {
            "subItem": [
              {
                "img": "/content/dam/oasis/page/2024/nav/common/Green-watch-2r-0816.png",
                "newPage": "_blank",
                "link": "/buy-oneplus-watch-2r",
                "name": "OnePlus Watch 2R"
              },
              {
                "img": "/content/dam/oasis/page/2024/nav/common/almond-silver.png",
                "newPage": "_blank",
                "link": "/buy-oneplus-watch-2",
                "name": "OnePlus Watch 2"
              }
            ]
          }
        ],
        "navItemLink": "/store/wearables"
      },
      {
        "0": "-",
        "rightItems": [
          {
            "subItem": [
              {
                "newPage": "_blank",
                "text": "Invite Friends",
                "textLink": "/invite-friends?activityId=21"
              },
              {
                "newPage": "_blank",
                "text": "Trade-in \u0026 Upgrade",
                "textLink": "/trade-in"
              },
              {
                "newPage": "_blank",
                "text": "Students Discounts",
                "textLink": "/discount-program?verify\u003deducation_program"
              },
              {
                "newPage": "_blank",
                "text": "Employee Discounts",
                "textLink": "/employee-groups?verify\u003demployee"
              },
              {
                "newPage": "_blank",
                "text": "Membership Benefits",
                "textLink": "/rcc#/membership"
              },
              {
                "newPage": "_blank",
                "text": "Exclusive savings in OnePlus Store App",
                "textLink": "https://www.oneplus.com/us/deep-link?app_page\u003doneplusstore%3A%2F%2Foneplus%2Fapp%3Furi%3Dcom.oneplus.mall.action.main%26tabIndex%3D0"
              }
            ],
            "title": "Offers"
          }
        ],
        "navImg": "/content/dam/oasis/page/2024/nav/common/audio.png",
        "nav": "Audio",
        "isDtb": "0",
        "leftTitle": "Explore All Audio",
        "navType": "commonNav",
        "secondTitleLink": "/store/audio",
        "leftItems": [
          {
            "subItem": [
              {
                "img": "/content/dam/oasis/page/2024/nav/in/white.png",
                "newPage": "_blank",
                "link": "/product/oneplus-buds-pro-3",
                "name": "OnePlus Buds Pro 3"
              },
              {
                "img": "/content/dam/oasis/page/2024/nav/in/green-buds3-pro.png",
                "newPage": "_blank",
                "link": "/product/oneplus-nord-buds-3-pro",
                "name": "OnePlus Nord Buds 3 Pro"
              },
              {
                "img": "/content/dam/oasis/page/2024/nav/in/eular.png",
                "newPage": "_blank",
                "link": "/product/oneplus-buds-3",
                "name": "OnePlus Buds 3"
              }
            ]
          }
        ],
        "navItemLink": "/store/audio",
        "navNewPage": "_self",
        "rightTitle": "Compare Audio",
        "titleLink": "/audio/compare"
      },
      {
        "0": "-",
        "rightItems": [
          {
            "subItem": [
              {
                "newPage": "_blank",
                "text": "Invite Friends",
                "textLink": "/invite-friends?activityId=21"
              },
              {
                "newPage": "_blank",
                "text": "Trade-in \u0026 Upgrade",
                "textLink": "/trade-in"
              },
              {
                "newPage": "_blank",
                "text": "Students Discounts",
                "textLink": "/discount-program?verify\u003deducation_program"
              },
              {
                "newPage": "_blank",
                "text": "Employee Discounts",
                "textLink": "/employee-groups?verify\u003demployee"
              },
              {
                "newPage": "_blank",
                "text": "Membership Benefits",
                "textLink": "/rcc#/membership"
              },
              {
                "newPage": "_blank",
                "text": "Exclusive savings in OnePlus Store App",
                "textLink": "https://www.oneplus.com/us/deep-link?app_page\u003doneplusstore%3A%2F%2Foneplus%2Fapp%3Furi%3Dcom.oneplus.mall.action.main%26tabIndex%3D0"
              }
            ],
            "title": "Offers"
          }
        ],
        "navImg": "/content/dam/oasis/page/2024/nav/us/nav-more-products-NA.png",
        "nav": "More Products",
        "isDtb": "0",
        "leftTitle": "Explore More Products",
        "navType": "commonNav",
        "secondTitleLink": "/store/all",
        "leftItems": [
          {
            "subItem": [
              {
                "img": "/content/dam/oasis/page/2024/nav/us/waffle-case.png",
                "newPage": "_blank",
                "link": "/store/cases-protection",
                "name": "Cases \u0026 Protection"
              },
              {
                "img": "/content/dam/oasis/page/2022/nav/power-cables/NA-Power-Cables.png",
                "newPage": "_blank",
                "link": "/store/power-cables",
                "name": "Power \u0026 Cables"
              },
              {
                "img": "/content/dam/oasis/page/2024/nav/common/bundles.png",
                "newPage": "_blank",
                "link": "/store/bundles",
                "name": "Bundles"
              }
            ]
          }
        ],
        "navItemLink": "/store/all"
      },
      {
        "0": "-",
        "rightItems": [
          {
            "subItem": [
              {
                "newPage": "_blank",
                "text": "Invite Friends",
                "textLink": "/invite-friends?activityId=21"
              },
              {
                "newPage": "_blank",
                "text": "Trade-in \u0026 Upgrade",
                "textLink": "/trade-in"
              },
              {
                "newPage": "_blank",
                "text": "Students Discounts",
                "textLink": "/discount-program?verify\u003deducation_program"
              },
              {
                "newPage": "_blank",
                "text": "Employee Discounts",
                "textLink": "/employee-groups?verify\u003demployee"
              },
              {
                "newPage": "_blank",
                "text": "Membership Benefits",
                "textLink": "/rcc#/membership"
              },
              {
                "newPage": "_blank",
                "text": "Exclusive savings in OnePlus Store App",
                "textLink": "https://www.oneplus.com/us/deep-link?app_page\u003doneplusstore%3A%2F%2Foneplus%2Fapp%3Furi%3Dcom.oneplus.mall.action.main%26tabIndex%3D0"
              }
            ],
            "title": "Offers"
          }
        ],
        "navImg": "/content/dam/oasis/page/2024/nav/common/onetopia.png",
        "nav": "OneTopia",
        "isDtb": "0",
        "leftTitle": "Evolve your digital life",
        "navType": "commonNav",
        "secondTitleLink": "/oneplus-featuring",
        "leftItems": [
          {
            "title": "Brand",
            "titleLink": "/brand"
          },
          {
            "title": "2024 OnePlus Photography Awards",
            "titleLink": "/photography-awards-2024/winners"
          },
          {
            "title": "OxygenOS",
            "titleLink": "/oxygenos15"
          }
        ],
        "navItemLink": "/oneplus-featuring"
      },
      {
        "0": "-",
        "rightItems": [
          {
            "subItem": [
              {
                "newPage": "_blank",
                "text": "Invite Friends",
                "textLink": "/invite-friends?activityId=21"
              },
              {
                "newPage": "_blank",
                "text": "Trade-in \u0026 Upgrade",
                "textLink": "/trade-in"
              },
              {
                "newPage": "_blank",
                "text": "Students Discounts",
                "textLink": "/discount-program?verify\u003deducation_program"
              },
              {
                "newPage": "_blank",
                "text": "Employee Discounts",
                "textLink": "/employee-groups?verify\u003demployee"
              },
              {
                "newPage": "_blank",
                "text": "Membership Benefits",
                "textLink": "/rcc#/membership"
              },
              {
                "newPage": "_blank",
                "text": "Exclusive savings in OnePlus Store App",
                "textLink": "https://www.oneplus.com/us/deep-link?app_page\u003doneplusstore%3A%2F%2Foneplus%2Fapp%3Furi%3Dcom.oneplus.mall.action.main%26tabIndex%3D0"
              }
            ],
            "title": "Offers"
          }
        ],
        "navImg": "/content/dam/oasis/page/2024/nav/common/community.png",
        "nav": "Community",
        "isDtb": "0",
        "leftTitle": "Welcome to OnePlus Community",
        "navType": "commonNav",
        "secondTitleLink": "https://community.oneplus.com",
        "leftItems": [
          {
            "title": "Community",
            "titleLink": "https://community.oneplus.com"
          },
          {
            "title": "Power of Community",
            "titleLink": "https://community.oneplus.com/powerOfCommunity?utm_medium\u003dnavigation\u0026utm_source\u003dofficial_website"
          }
        ],
        "navItemLink": "https://community.oneplus.com"
      },
      {
        "0": "-",
        "rightItems": [
          {
            "subItem": [
              {
                "newPage": "_blank",
                "text": "Invite Friends",
                "textLink": "/invite-friends?activityId=21"
              },
              {
                "newPage": "_blank",
                "text": "Trade-in \u0026 Upgrade",
                "textLink": "/trade-in"
              },
              {
                "newPage": "_blank",
                "text": "Students Discounts",
                "textLink": "/discount-program?verify\u003deducation_program"
              },
              {
                "newPage": "_blank",
                "text": "Employee Discounts",
                "textLink": "/employee-groups?verify\u003demployee"
              },
              {
                "newPage": "_blank",
                "text": "Membership Benefits",
                "textLink": "/rcc#/membership"
              },
              {
                "newPage": "_blank",
                "text": "Exclusive savings in OnePlus Store App",
                "textLink": "https://www.oneplus.com/us/deep-link?app_page\u003doneplusstore%3A%2F%2Foneplus%2Fapp%3Furi%3Dcom.oneplus.mall.action.main%26tabIndex%3D0"
              }
            ],
            "title": "Offers"
          }
        ],
        "navImg": "/content/dam/oasis/page/2024/nav/common/service.png",
        "nav": "Support",
        "isDtb": "0",
        "leftTitle": "Welcome to OnePlus Support",
        "navType": "commonNav",
        "secondTitleLink": "/support",
        "leftItems": [
          {
            "title": "Repair Service",
            "titleLink": "/support/repair"
          },
          {
            "title": "Protection Plan",
            "titleLink": "/support/product/protection-service"
          },
          {
            "title": "Repair Pricing",
            "titleLink": "/support/repair-pricing"
          }
        ],
        "navItemLink": "/support"
      }
    ],
    "cartDisplay": "1",
    "searchDisplay": "0",
    "achievementDisplay": "1",
    "accountDisplay": "1",
    "membersItems": [
      {
        "isDtb": "0",
        "newPage": "_self",
        "icon": "/content/dam/oasis/coupon.svg",
        "link": "/customer/voucher#/",
        "title": "Coupon"
      },
      {
        "isDtb": "0",
        "newPage": "_self",
        "icon": "https://image01.oneplus.net/shop/202206/17/1-M00-3D-5A-rB8LB2KsMNmAAfPlAAAHqNPsTMM380.svg",
        "link": "/redcoins-center#/",
        "title": "RedCoins"
      },
      {
        "isDtb": "0",
        "newPage": "_self",
        "icon": "https://image01.oneplus.net/shop/202206/17/1-M00-3D-5A-rB8bwmKsMNiAJlS1AAADtHsnzMM635.svg",
        "link": "/redcableclub",
        "title": "Red Cable Club"
      }
    ]
  }</script>
<script src="https://www.oneplus.com/content/dam/statics/oasis/header/vendor.js"></script>
<script src="https://www.oneplus.com/content/dam/statics/oasis/header/layout.js"></script>
<style>
  .header-v3 .phone-hidden {
    display: none!important;
  }

  #preLoadPopper, .notice-ribbon {
    display: none;
  }
</style>
<style>
  .cmp__hero-banner-jimu .hero-banner-container .hero-banner-swiper-container .hero-banner-swiper-wrapper .hero-banner-swiper-slide .banner-img {
    height: 650px !important;
  }

  .cmp__hero-banner-jimu .hero-banner-container .hero-banner-swiper-container .hero-banner-max-height .homepage-banner-swiper-slide .banner-img {
    height: 750px !important;
  }

  .cmp__hero-banner-jimu .hero-banner-container .hero-banner-swiper-container .hero-banner-swiper-wrapper .homepage-banner-slide-around .banner-img {
    height: 650px !important;
  }

  .cmp__hero-banner-jimu .hero-banner-container .hero-banner-swiper-container .hero-banner-max-height .homepage-banner-slide-around .banner-img {
    height: 650px !important;
  }

  @media (max-width: 1439.98px) {
    .cmp__hero-banner-jimu .hero-banner-container .hero-banner-swiper-container .hero-banner-swiper-wrapper .hero-banner-swiper-slide .banner-img {
      height: 500px !important;
    }

    .cmp__hero-banner-jimu .hero-banner-container .hero-banner-swiper-container .hero-banner-max-height .homepage-banner-swiper-slide .banner-img {
      height: 600px !important;
    }

    .cmp__hero-banner-jimu .hero-banner-container .hero-banner-swiper-container .hero-banner-swiper-wrapper .homepage-banner-slide-around .banner-img {
      height: 500px !important;
    }

    .cmp__hero-banner-jimu .hero-banner-container .hero-banner-swiper-container .hero-banner-max-height .homepage-banner-slide-around .banner-img {
      height: 500px !important;
    }
  }

  @media (max-width: 1023.98px) {
    .cmp__hero-banner-jimu .hero-banner-container .hero-banner-swiper-container .hero-banner-swiper-wrapper .hero-banner-swiper-slide .banner-img {
      height: 500px !important;
    }

    .cmp__hero-banner-jimu .hero-banner-container .hero-banner-swiper-container .hero-banner-max-height .homepage-banner-swiper-slide .banner-img {
      height: 600px !important;
    }

    .cmp__hero-banner-jimu .hero-banner-container .hero-banner-swiper-container .hero-banner-swiper-wrapper .homepage-banner-slide-around .banner-img {
      height: 700px !important;
    }

    .cmp__hero-banner-jimu .hero-banner-container .hero-banner-swiper-container .hero-banner-max-height .homepage-banner-slide-around .banner-img {
      height: 700px !important;
    }
  }

  @media (max-width: 649.98px) {
    .cmp__hero-banner-jimu .hero-banner-container .hero-banner-swiper-container .hero-banner-swiper-wrapper .hero-banner-swiper-slide .banner-img {
      height: 550px !important;
    }

    .cmp__hero-banner-jimu .hero-banner-container .hero-banner-swiper-container .hero-banner-max-height .homepage-banner-swiper-slide .banner-img {
      height: 650px !important;
    }

    .cmp__hero-banner-jimu .hero-banner-container .hero-banner-swiper-container .hero-banner-swiper-wrapper .homepage-banner-slide-around .banner-img {
      height: 700px !important;
    }

    .cmp__hero-banner-jimu .hero-banner-container .hero-banner-swiper-container .hero-banner-max-height .homepage-banner-slide-around .banner-img {
      height: 700px !important;
    }
  }

  .cmp__hero-banner-jimu .hero-banner-container .hero-banner-swiper-container .hero-banner-swiper-wrapper .hero-banner-swiper-slide .banner-img .banner {
    max-width: unset !important;
    height: auto !important;
    width: unset !important;
    transform: scale(33.33%) !important;
    object-fit: cover !important;
  }

  .cmp__hero-banner-jimu .hero-banner-container .hero-banner-swiper-container .hero-banner-swiper-wrapper .upper .banner-img .banner {
    transform-origin: bottom !important;
  }

  .cmp__hero-banner-jimu .hero-banner-container .hero-banner-swiper-container .hero-banner-swiper-wrapper .center .banner-img .banner {
    transform-origin: center !important;
  }

  .cmp__hero-banner-jimu .hero-banner-container .hero-banner-swiper-container .hero-banner-swiper-wrapper .down .banner-img .banner {
    transform-origin: top !important;
  }
</style>
<div class="root responsivegrid">
  <script>
    window.pageDsl = {
      "cmps": ["CmpNavigation_e3925461", "CmpHtmlScript_9b0495a0", "CmpHtmlScript_c27ee6ec"],
      "byId": {
        "CmpNavigation_e3925461": {
          "name": "Navigation_e3925461",
          "style": {
            "theme": "theme-light",
            "showInApp": "app-display"
          },
          "id": "CmpNavigation_e3925461",
          "type": "CmpNavigation",
          "attr": {
            "navigation": {
              "subItem": [{
                "subMainItem": {
                  "btnName": "Performance",
                  "link": {
                    "webLink": "#performance"
                  },
                  "clickType": "bindEvent",
                  "showFlag": false
                }
              }, {
                "subMainItem": {
                  "btnName": "Battery",
                  "link": {
                    "webLink": "#battery"
                  },
                  "clickType": "bindEvent",
                  "showFlag": false
                }
              }, {
                "subMainItem": {
                  "btnName": "Display",
                  "link": {
                    "webLink": "#display"
                  },
                  "clickType": "bindEvent",
                  "showFlag": false
                }
              }, {
                "subMainItem": {
                  "btnName": "Photography",
                  "link": {
                    "webLink": "#photo"
                  },
                  "clickType": "bindEvent",
                  "showFlag": false
                }
              }, {
                "subMainItem": {
                  "btnName": "AI",
                  "link": {
                    "webLink": "#AI"
                  },
                  "clickType": "bindEvent",
                  "showFlag": false
                }
              }, {
                "subMainItem": {
                  "btnName": "Design",
                  "link": {
                    "webLink": "#design"
                  },
                  "clickType": "bindEvent",
                  "showFlag": false
                }
              }, {
                "subMainItem": {
                  "btnName": "OxygenOS",
                  "link": {
                    "webLink": "#OS"
                  },
                  "clickType": "bindEvent",
                  "showFlag": false
                }
              }],
              "mianItem": [{
                "mainItem": {
                  "btnName": "OnePlus 13",
                  "openInApp": "inApp",
                  "openInBrowser": "currentTab",
                  "appLinkType": "h5",
                  "link": {
                    "webLink": "/13",
                    "membershipLink": "",
                    "storeLink": "",
                    "inAppWebLink": "/13"
                  },
                  "clickType": "link",
                  "showFlag": false
                }
              }],
              "buttonItem": [{
                "mainButtonItem": {
                  "btnName": "Buy now",
                  "openInApp": "inApp",
                  "openInBrowser": "newTab",
                  "appLinkType": "h5",
                  "link": {
                    "webLink": "/oneplus-13",
                    "membershipLink": "",
                    "storeLink": "",
                    "inAppWebLink": "/oneplus-13"
                  },
                  "clickType": "link",
                  "showFlag": false
                }
              }, {
                "mainButtonItem": {
                  "btnName": "Specs",
                  "openInApp": "inApp",
                  "openInBrowser": "newTab",
                  "appLinkType": "h5",
                  "link": {
                    "webLink": "/13/specs",
                    "membershipLink": "",
                    "storeLink": "",
                    "inAppWebLink": "/13/specs"
                  },
                  "clickType": "link",
                  "showFlag": false
                }
              }, {
                "mainButtonItem": {
                  "btnName": "Overview",
                  "openInApp": "inApp",
                  "openInBrowser": "currentTab",
                  "appLinkType": "h5",
                  "link": {
                    "webLink": "/13",
                    "membershipLink": "",
                    "storeLink": "",
                    "inAppWebLink": "/13"
                  },
                  "clickType": "link",
                  "showFlag": false
                }
              }]
            }
          }
        }
      },
      "i18n": {
        "yes": "yes",
        "no": "no",
        "close": "close",
        "delete": "delete",
        "edit": "edit",
        "add": "add",
        "search": "search",
        "reset": "reset",
        "confirm": "confirm",
        "cancel": "cancel",
        "Sorry, the prize distribution is abnormal. Please contact customer service to resolve.": "Sorry, the prize distribution is abnormal. Please contact customer service to resolve.",
        "Facebook": "Facebook",
        "Whatsapp": "Whatsapp",
        "X": "X",
        "Instagram": "Instagram",
        "Save Image": "Save Image",
        "Copy Link": "Copy Link",
        "Please participate in this activity after completing the {0}.": "Please participate in this activity after completing the {0}.",
        "Go": "Go",
        "Rules": "Rules",
        "Prize list": "Prize list",
        "No prize information": "No prize information",
        "Please sign in before participating in the activity.": "Please sign in before participating in the activity.",
        "Link copied": "Link copied",
        "Use Now": "Use Now",
        "Cancel": "Cancel",
        "View now": "View now",
        "You can participate in the event after completing brows specify pages.": "You can participate in the event after completing brows specify pages.",
        "You can participate in the event after share.": "You can participate in the event after share.",
        "Share now": "Share now",
        "Go it": "Go it",
        "code:": "code:",
        "Copied successfully": "Copied successfully",
        "Sign in": "Sign in",
        "Done": "Done",
        "Show more": "Show more",
        "Collapse": "Collapse",
        "Buy now": "Buy now",
        "View product": "View product",
        "Pick {0} items from below": "Pick {0} items from below",
        "Pick an item from below": "Pick an item from below",
        "Stop sale": "Stop sale",
        "Out of stock": "Out of stock",
        "App Exclusive": "App Exclusive",
        "Save {0}": "Save {0}",
        "Free Gift.": "Free Gift.",
        "Fixed gift": "Fixed gift",
        "Optional gift": "Optional gift",
        "New": "New",
        "{0}% OFF": "{0}% OFF",
        "Price unrevealed": "Price unrevealed",
        "inclusive of all taxes": "inclusive of all taxes",
        "Sorry, we currently have insufficient stock of gift items. Try reaching out to our customer service or check the inventory again at a later time.": "Sorry, we currently have insufficient stock of gift items. Try reaching out to our customer service or check the inventory again at a later time.",
        "Confirm": "Confirm",
        "Learn more": "Learn more",
        "Prize is being distributed, please wait a moment.": "Prize is being distributed, please wait a moment.",
        "The current app version does not support this feature. Please upgrade to the latest version of the OnePlus Store or OnePlus Red Cable Club app. You can also share a screenshot.": "The current app version does not support this feature. Please upgrade to the latest version of the OnePlus Store or OnePlus Red Cable Club app. You can also share a screenshot.",
        "File saved.": "File saved.",
        "The current app version does not support this feature. You can share a screenshot.": "The current app version does not support this feature. You can share a screenshot.",
        "Your phone number": "Your phone number",
        "Your email address": "Your email address",
        "Subscribe": "Subscribe",
        "Enter phone number": "Enter phone number",
        "We’ve identified your account email. Please confirm permission to use it.": "We’ve identified your account email. Please confirm permission to use it.",
        "We’ve identified your account phone number. Please confirm permission to use it.": "We’ve identified your account phone number. Please confirm permission to use it.",
        "From your account": "From your account",
        "Use a new email": "Use a new email",
        "Use a new phone number": "Use a new phone number",
        "Agree to subscribe": "Agree to subscribe",
        "show this page to": "show this page to",
        "Enter the email to subscribe": "Enter the email to subscribe",
        "Enter the phone number to subscribe": "Enter the phone number to subscribe",
        "Google Play": "Google Play",
        "Verification Code": "Verification Code",
        "Enter verification code": "Enter verification code",
        "Please enter a valid Verification Code": "Please enter a valid Verification Code",
        "This email has been subscribed please use a new one": "This email has been subscribed please use a new one",
        "This phone number has been subscribed please use a new one": "This phone number has been subscribed please use a new one",
        "Verification code error": "Verification code error",
        "Subscription successful": "Subscription successful",
        "Get code": "Get code",
        "s": "s",
        "Use login phone": "Use login phone",
        "Use login email": "Use login email",
        "Please use the correct email": "Please use the correct email",
        "Please use the correct phone number": "Please use the correct phone number",
        "Oops, the spaceship just got lost! We are trying to get it back to earth.": "Oops, the spaceship just got lost! We are trying to get it back to earth.",
        "Successfully subscribed": "Successfully subscribed",
        "Use account phone number": "Use account phone number",
        "Use account email": "Use account email",
        "Subscription Notice": "Subscription Notice",
        "Yes, I would like to receive a marketing email from OnePlus and agree to the ": "Yes, I would like to receive a marketing email from OnePlus and agree to the ",
        "privacy policy": "privacy policy",
        "OnePlus may create personal profiles from my purchase and usage behavior.<br>This means that advertising and websites are better tailored to my personal interests.<br>Please read and agree to the ": "OnePlus may create personal profiles from my purchase and usage behavior.<br>This means that advertising and websites are better tailored to my personal interests.<br>Please read and agree to the ",
        "Disagree": "Disagree",
        "Agree and subscribe": "Agree and subscribe",
        "Notify Me Successfully!": "Notify Me Successfully!",
        "Notify Me": "Notify Me",
        "This phone number has already been notified. Please use a different one.": "This phone number has already been notified. Please use a different one.",
        "This email has already been notified. Please use a different one.": "This email has already been notified. Please use a different one.",
        "Enter your phone number to notify me.": "Enter your phone number to notify me.",
        "Enter your email to notify me.": "Enter your email to notify me.",
        "I agree to be notified.": "I agree to be notified.",
        "I agree and wish to be notified.": "I agree and wish to be notified.",
        "Notify Me Successful!": "Notify Me Successful!",
        "With Gift": "With Gift",
        "Save Up To {0}": "Save Up To {0}",
        "From {0}": "From {0}",
        "or {0}/mo. with NCEMI": "or {0}/mo. with NCEMI",
        "or {0}/mo. with LCEMI": "or {0}/mo. with LCEMI",
        "or {0}/mo.": "or {0}/mo.",
        "Link failed": "Link failed",
        "Upgrade now": "Upgrade now",
        "Proceed with linking this device with the current account": "Proceed with linking this device with the current account",
        "Log out of the current account and sign in with the previous one": "Log out of the current account and sign in with the previous one",
        "Contact us": "Contact us",
        "Email": "Email",
        "Online chat": "Online chat",
        "contact customer service": "contact customer service",
        "Please input a valid code.": "Please input a valid code.",
        "Failed to claim, the maximum claim limit has been reached.": "Failed to claim, the maximum claim limit has been reached.",
        "You have already redeemed this code.": "You have already redeemed this code.",
        "You have already applied an invitation code to your account.": "You have already applied an invitation code to your account.",
        "Claim benefit": "Claim benefit",
        "Claim from": "Claim from",
        "Claim": "Claim",
        "or": "or",
        "Claim with code": "Claim with code",
        "There is no sharing record yet.": "There is no sharing record yet.",
        "Time range": "Time range",
        "Share info": "Share info",
        "{0} remaining sharing times, Shared {1} times.": "{0} remaining sharing times, Shared {1} times.",
        "View sharing history": "View sharing history",
        "Claim info": "Claim info",
        "View claim record": "View claim record",
        "Benefits Description": "Benefits Description",
        "View all available {0} stores.": "View all available {0} stores.",
        "Share benefit": "Share benefit",
        "Share your link/code to your friends.": "Share your link/code to your friends.",
        "Copy code": "Copy code",
        "Copy link": "Copy link",
        "Store addresses": "Store addresses",
        "My invitation record": "My invitation record",
        "The following users have successfully claimed your rights": "The following users have successfully claimed your rights",
        "My claim record": "My claim record",
        "Claim Now": "Claim Now",
        "Verify and claim": "Verify and claim",
        "Used": "Used",
        "Expired": "Expired",
        "Check my RedCoins": "Check my RedCoins",
        "Check Coupon": "Check Coupon",
        "Sign in to Claim": "Sign in to Claim",
        "Successfully claimed": "Successfully claimed",
        "Claimed failed": "Claimed failed",
        "Code": "Code",
        "Benefits Details": "Benefits Details",
        "Sorry, your device is not eligible to share.": "Sorry, your device is not eligible to share.",
        "Sorry, your user ID is not eligible to share.": "Sorry, your user ID is not eligible to share.",
        "Sorry, your first binding time is not eligible.": "Sorry, your first binding time is not eligible.",
        "Sorry, you are not eligible to share. Please add phone number to your OnePlus account.": "Sorry, you are not eligible to share. Please add phone number to your OnePlus account.",
        "Congratulations, {0} RedCoins added to your account": "Congratulations, {0} RedCoins added to your account",
        "Please check back later to claim this benefit after {0}": "Please check back later to claim this benefit after {0}",
        "This benefit is not available in your region.": "This benefit is not available in your region.",
        "Null Device ID: Please log in to your OnePlus account again through settings> account or contact customer service for help": "Null Device ID: Please log in to your OnePlus account again through settings> account or contact customer service for help",
        "This benefit is not available on your device.": "This benefit is not available on your device.",
        "You have reached your limit for this benefit.": "You have reached your limit for this benefit.",
        "Upgrade to unlock.": "Upgrade to unlock.",
        "All available quantities for this benefit have been claimed by eligible users.": "All available quantities for this benefit have been claimed by eligible users.",
        "All available quantities for this benefit have been claimed.": "All available quantities for this benefit have been claimed.",
        "Please upgrade your RCC App or contact customer service for help": "Please upgrade your RCC App or contact customer service for help",
        "Error message": "Error message",
        "Sign in to unlock your membership benefits!": "Sign in to unlock your membership benefits!",
        "Sorry, you are not eligible to claim compensation benefits": "Sorry, you are not eligible to claim compensation benefits",
        "Only mobile devices are eligible to claim this benefits": "Only mobile devices are eligible to claim this benefits",
        "Only available for first-time link users during the activity period": "Only available for first-time link users during the activity period",
        "Only first-time linked OnePlus accounts are eligible for this benefit.": "Only first-time linked OnePlus accounts are eligible for this benefit.",
        "Please link your email to the OnePlus account to claim the benefits.": "Please link your email to the OnePlus account to claim the benefits.",
        "Please link your mobile phone number to the OnePlus account to claim the benefits.": "Please link your mobile phone number to the OnePlus account to claim the benefits.",
        "This device is currently linked with {0}. Please verify if you wish to link this device to a different account. Should you proceed, the benefits earned or claimed through the previously linked account will not be transferred to this account, nor will you earn any RedCoins or RedExp points on linking this device as they were credited to the previous account. {1}": "This device is currently linked with {0}. Please verify if you wish to link this device to a different account. Should you proceed, the benefits earned or claimed through the previously linked account will not be transferred to this account, nor will you earn any RedCoins or RedExp points on linking this device as they were credited to the previous account. {1}",
        "Network Error: Please check your network and try again": "Network Error: Please check your network and try again",
        "Unsupported Device Model: Your device model is not supported to link, please contact customer service for help": "Unsupported Device Model: Your device model is not supported to link, please contact customer service for help",
        "NULL SN: Please link your device through Settings > Red Cable Club or upgrade your RCC App": "NULL SN: Please link your device through Settings > Red Cable Club or upgrade your RCC App",
        "Account Limit Exceeded: Your account has exceeded the maximum devices limit, please switch to another OnePlus account to link": "Account Limit Exceeded: Your account has exceeded the maximum devices limit, please switch to another OnePlus account to link",
        "Limit Exceeded Today: Unable to link device to your account as it has reached its maximum capacity today, please link your device 24 hours later": "Limit Exceeded Today: Unable to link device to your account as it has reached its maximum capacity today, please link your device 24 hours later",
        "Device Already Linked: You’ve successfully completed the linking process. Kindly refresh your webpage": "Device Already Linked: You’ve successfully completed the linking process. Kindly refresh your webpage",
        "Invalid SN: Please contact customer service for help": "Invalid SN: Please contact customer service for help",
        "Link Failed: Please upgrade your RCC App or contact customer service for help": "Link Failed: Please upgrade your RCC App or contact customer service for help",
        "Null IMEI：Please link your device through Settings > Red Cable Club or upgrade your RCC App": "Null IMEI：Please link your device through Settings > Red Cable Club or upgrade your RCC App",
        "User ID Null: Please log in to your OnePlus account again through settings> account or contact customer service for help": "User ID Null: Please log in to your OnePlus account again through settings> account or contact customer service for help",
        "Get User INFO Failed: Please log in to your OnePlus account again through settings> account or contact customer service for help": "Get User INFO Failed: Please log in to your OnePlus account again through settings> account or contact customer service for help",
        "Null Parameter: Please upgrade your RCC App or contact customer service for help": "Null Parameter: Please upgrade your RCC App or contact customer service for help",
        "Unmatched IMEI Region: Please contact our customer service for further help": "Unmatched IMEI Region: Please contact our customer service for further help",
        "IMEI Error: Please upgrade your RCC App or contact customer service for help": "IMEI Error: Please upgrade your RCC App or contact customer service for help",
        "Null Device ID: Please contact our customer service for further help": "Null Device ID: Please contact our customer service for further help",
        "Link Failed: This device is already linked by xxxxx. Please contact our customer service for help": "Link Failed: This device is already linked by xxxxx. Please contact our customer service for help",
        "Generate Failed: Please log in to your OnePlus account again through settings> account or contact customer service for help": "Generate Failed: Please log in to your OnePlus account again through settings> account or contact customer service for help",
        "Locking Error: Please contact our customer service for further help": "Locking Error: Please contact our customer service for further help",
        "Unlink Failed: Your device has been linked by xxxx, please contact customer service for help": "Unlink Failed: Your device has been linked by xxxx, please contact customer service for help",
        "Null Region: Please contact our customer service for further help": "Null Region: Please contact our customer service for further help",
        "Link Failed: Please contact customer service for help": "Link Failed: Please contact customer service for help",
        "Unlock Failed, Please contact our customer service for further help": "Unlock Failed, Please contact our customer service for further help",
        "Please upgrade your RCC App to link your device!": "Please upgrade your RCC App to link your device!",
        "Service Tel:": "Service Tel:",
        "Null benefit code": "Null benefit code",
        "Null asset code": "Null asset code",
        "Misallocation of benefits": "Misallocation of benefits",
        "Claims have been made too often. Please try again later": "Claims have been made too often. Please try again later",
        "You have not reached the level where you can claim this benefit": "You have not reached the level where you can claim this benefit",
        "Benefits are not yet available, please check back later": "Benefits are not yet available, please check back later",
        "Sorry! This benefit has expired": "Sorry! This benefit has expired",
        "Sorry. This benefit is not available now": "Sorry. This benefit is not available now",
        "Sorry, you have reached your limit for this benefit": "Sorry, you have reached your limit for this benefit",
        "Sorry, This benefit is not available on your device": "Sorry, This benefit is not available on your device",
        "All available quantities for this benefit have been claimed": "All available quantities for this benefit have been claimed",
        "All available quantities for this benefit have been claimed by other users": "All available quantities for this benefit have been claimed by other users",
        "Please link your device to claim this benefits": "Please link your device to claim this benefits",
        "Failed to claim! Please contact customer service for help": "Failed to claim! Please contact customer service for help",
        "Sorry! This device’s extended warranty has expired": "Sorry! This device’s extended warranty has expired",
        "Failed to issue the asset. Please contact customer service.": "Failed to issue the asset. Please contact customer service.",
        "The asset has expired": "The asset has expired",
        "The asset stock insufficient": "The asset stock insufficient",
        "Insufficient user level": "Insufficient user level",
        "You have claimed the asset": "You have claimed the asset",
        "The asset has laspsed": "The asset has laspsed",
        "The operation of claiming benefits is too frequent, please try again later": "The operation of claiming benefits is too frequent, please try again later",
        "The lottery is in progress, please check the results later": "The lottery is in progress, please check the results later",
        "View my prize": "View my prize",
        "Back to Live": "Back to Live",
        "View Coupon Center": "View Coupon Center",
        "View Redcoins Center": "View Redcoins Center",
        "Unfortunately": "Unfortunately",
        "It's a pity that I didn't win the prize": "It's a pity that I didn't win the prize",
        "Login to participate": "Login to participate",
        "End": "End",
        "Prize Countdown": "Prize Countdown",
        "Start after<br />{0}mins of live": "Start after<br />{0}mins of live",
        "View the result of this round": "View the result of this round",
        "Watch for at least {0}mins to participate": "Watch for at least {0}mins to participate",
        "Claim failure": "Claim failure",
        "You have earned reward, but the claim has failed.": "You have earned reward, but the claim has failed.",
        "Please contact online customer service to resend the prize.": "Please contact online customer service to resend the prize.",
        "Fill in your birthday": "Fill in your birthday",
        "Fill in now": "Fill in now",
        "Please fill in your date of birth to participate in the lottery.": "Please fill in your date of birth to participate in the lottery.",
        "Sorry, you can modify your birthday only once per year": "Sorry, you can modify your birthday only once per year",
        "Date of birth": "Date of birth",
        "Day": "Day",
        "Year": "Year",
        "Rules & Prizes": "Rules & Prizes",
        "My prizes": "My prizes",
        "Get more draw chance": "Get more draw chance",
        "Chances": "Chances",
        "Starting at {0}": "Starting at {0}",
        "End in {0}": "End in {0}",
        "Activity has ended": "Activity has ended",
        "Chances:": "Chances:",
        "Won {0} {1}": "Won {0} {1}",
        "No award information": "No award information",
        "Sign in and play": "Sign in and play",
        "Coming soon": "Coming soon",
        "Play now": "Play now",
        "Ended": "Ended",
        "RedCoins:": "RedCoins:",
        "My Prizes": "My Prizes",
        "Task List": "Task List",
        "How to participate in the event?": "How to participate in the event?",
        "Redeem lucky draw opportunities": "Redeem lucky draw opportunities",
        "Sorry,the prize distribution is abnormal, please contact customer service to resolve it.": "Sorry,the prize distribution is abnormal, please contact customer service to resolve it.",
        "View": "View",
        "Please link your device and participate in this RCC members exclusive event.": "Please link your device and participate in this RCC members exclusive event.",
        "Tips: If you can't open the RCC page, please update your system version or": "Tips: If you can't open the RCC page, please update your system version or",
        "upgrade your RCC APP": "upgrade your RCC APP",
        ", if you need further help, please contact": ", if you need further help, please contact",
        "OnePlus customer service": "OnePlus customer service",
        "Join now": "Join now",
        "Unable to open rcc, please check the device according to the pop-up prompt": "Unable to open rcc, please check the device according to the pop-up prompt",
        "Use {0} to redeem play chance": "Use {0} to redeem play chance",
        "My redcoins:": "My redcoins:",
        "Total:": "Total:",
        "RedCoins": "RedCoins",
        "Redeem": "Redeem",
        "Go now": "Go now",
        "Play again": "Play again",
        "Back to activity": "Back to activity",
        "View in my account": "View in my account",
        "Copy Success": "Copy Success",
        "You can get {0}/{1} play chance": "You can get {0}/{1} play chance",
        "play chance": "play chance",
        "Sorry, your reward failed to be issued. We will try to reissue it for you. You can also contact customer service for support.": "Sorry, your reward failed to be issued. We will try to reissue it for you. You can also contact customer service for support.",
        "Twitter": "Twitter",
        "You didn't win anything. Better luck next time!": "You didn't win anything. Better luck next time!",
        "Uh-oh!": "Uh-oh!",
        "GO": "GO",
        "Completed": "Completed",
        "Share": "Share",
        "Participate in the {0}": "Participate in the {0}",
        "Browse {0} page": "Browse {0} page",
        "Share {0} to social media": "Share {0} to social media",
        "Your RedCoins are insufficient.": "Your RedCoins are insufficient.",
        "Insufficient redemption times available.": "Insufficient redemption times available.",
        "You've got {0} chances to draw.": "You've got {0} chances to draw.",
        "Stay connected": "Stay connected",
        "Turn on your message notification and stay connected with new product releases, exclusive offers and more": "Turn on your message notification and stay connected with new product releases, exclusive offers and more",
        "No event participation opportunities": "No event participation opportunities",
        "Not enough RedCoins to enter the draw": "Not enough RedCoins to enter the draw",
        "Thank you": "Thank you"
      }
    }
  </script>
  <div id="app" storeviewcode="us" locale="en_US" website="www_oneplus_com">
    <!--[-->
    <div class="content-wrapper">
      <div class="vue-comp-container" vue-cmp="CmpNavigation" page-info="" cid="CmpNavigation_e3925461"></div>
    </div>
    <div class="content-wrapper">
      <section data-show-in-app="app-display" type="CmpHtmlScript" index="1">
        <style>
          body .navigation-container.fixed-position-not-hidden, body .navigation-container.fixed-position-hidden, body .app-navigation-container.fixed-position-not-hidden, body .app-navigation-container.fixed-position-hidden {
            top: 0;
            z-index: 100;
          }

          .navigation-container .point-tab {
            display: none;
          }
        </style>
        <div class="cmp-html-script">
          <script>
            window.addEventListener('scroll', function() {
              var scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
              if (scrollTop === 0) {
                document.getElementById('navigation-container') && document.getElementById('navigation-container').classList.remove('fixed-position-not-hidden')
                document.getElementById('app-navigation-container') && document.getElementById('app-navigation-container').classList.remove('fixed-position-not-hidden')
              }
            });

            document.addEventListener("DOMContentLoaded", function() {
              var priceArr = {
                us: '$899.99',
                ca_en: 'CA$1,249.99',
                ca_fr: 'CA$1,249.99'
              }

              // 获取当前站点
              var currentStore = document.getElementById('mark-current-store') && document.getElementById('mark-current-store').value
              // 匹配价格
              var nordPrice = priceArr[currentStore]
              // 获取价格div的from翻译文案
              var _price = document.querySelector('.price-text')
              // 拼接赋值
              if (nordPrice) {
                _price.textContent = _price.innerText.trim() + ' ' + nordPrice
              } else {
                _price.textContent = ''
              }
            })
          </script>
        </div>
      </section>
    </div>
    <div class="content-wrapper">
      <section data-show-in-app="app-display" type="CmpHtmlScript" index="2">
        <style>
          .price-text {
            font-size: 16px;
            color: #000;
            padding-top: 10px;
            margin: 0 auto;
          }

        </style>
        <div class="cmp-html-script">
          <!-- Version: 1.3.0 -->
          <main></main>
        </div>
      </section>
    </div>
    <!--]-->
  </div>
</div>
<div id="page-footer" class="hidden-in-member-app hidden-in-store-app web-6-class">
  <footer id="footer">
    <div class="container-text"></div>
    <div class="container-text">
      <div class="footer-nav">
        <dl v-for="(item, index) in products" :key="index">
          <dt class="nav-trigger collapsed font-body-md">
            {{item.title}}<i class="icon-fold"></i>
          </dt>
          <dd class="nav-slide">
            <ul>
              <li v-for="(product, index1) in item.productList" :key="index" class="font-note-sm white-55">
                <a tm-eventCategory="Bottom navigation" tm-eventAction="pageName" tm-eventLabel="Phones+OnePlus 11 5G" :href="product.link">{{product.name}}</a>
              </li>
            </ul>
          </dd>
        </dl>
      </div>
    </div>
    <div class="container-text">
      <!-- 中间层 一套pc 一套移动 -->
      <div>
        <!-- 大屏1024+ -->
        <div class="contact-us" v-if="!isFooterXs">
          <div class="payments-img-box">
            <template v-if="countryCookie !== 'in'">
              <img v-for="item in paymentIcons" :src="item" :key="item" alt="">
            </template>
            <template v-else>
              <div v-for="item in indiaIconList" :key="item.name" class="font-note-sm">
                <i :class="item.icon"></i>
                {{ item.name }}

              </div>
            </template>
          </div>
          <div class="customer-support">
            <p class="social-media">
              <a tm-eventCategory="Social media" tm-eventAction="pageName" tm-eventLabel="Facebook" href="https://facebook.com/oneplus" class="link-gray" target="_blank">
                <i class="ico ico-facebook-solid svg-icon"></i>
              </a>
              <a tm-eventCategory="Social media" tm-eventAction="pageName" tm-eventLabel="Twitter" href="https://twitter.com/oneplus" class="link-gray" target="_blank">
                <i class="ico ico-twitter-solid svg-icon"></i>
              </a>
              <a tm-eventCategory="Social media" tm-eventAction="pageName" tm-eventLabel="Instagram" href="https://www.instagram.com/oneplus" class="link-gray" target="_blank">
                <i class="ico ico-instagram-footer svg-icon"></i>
              </a>
            </p>
            <div class="div-line"></div>
            <div class="font-body-md white-95 open-chat-online-box">
              Get Support From OnePlus
              <i class="right-icon ico ico-arrow-right-footer"></i>
              <!-- 浮窗 -->
              <div class="chat-online-box">
                <!-- 欧美客服 -->
                <div id="customer-service-footer" onclick="onClickCustomerService()" class="item-box" style="display: none;">
                  <div class="font-body-md left-msg-box">
                    <div class="icon-title">
                      <i class="icon-btn-svg">
                        <img src="https://opsg-img-cdn-gl.heytapimg.com/epb/202210/12/NujgdjSAIGaFvgnH.svg" alt="">
                      </i>
                      <span>Contact us</span>
                    </div>
                    <div class="desc font-note-sm">9 am - 9 pm EST, Mon to Fri; 10 am - 8 pm EST, Sat to Sun</div>
                  </div>
                  <div class="icon-right-box">
                    <i class="ico ico-arrow-right-footer"></i>
                  </div>
                </div>
                <div v-if="siteBol && ONLINE_CHAT" class="online-chat" id="live-chat-footer" @click="onlineService" class="item-box">
                  <div class="font-body-md left-msg-box">
                    <div class="icon-title">
                      <i class="ico ico-Message"></i>
                      <span>Live Chat</span>
                    </div>
                  </div>
                  <div class="icon-right-box">
                    <i class="ico ico-arrow-right-footer"></i>
                  </div>
                </div>
                <!-- <div class="item-box" id="chat-old-class" style="display: none;">
    <div class="font-body-md left-msg-box">
        <div class="icon-title">
            <i class="ico ico-tab_Communication_Call "></i>
            <a class="black95"
                :href="getHrefTel(phoneTimeObj.phoneNumber)"><span>{{phoneTimeObj.phoneNumber}}</span></a>
        </div>
        <div v-for="(item,index) in phoneTimeObj.phoneTime" :key="index" class="desc font-note-sm">{{item}}</div>
    </div>
</div> -->
                <div class="item-box">
                  <div class="font-body-md left-msg-box">
                    <a href="https://oneplus.custhelp.com/app/ask">
                      <div class="icon-title black95">
                        <i class="ico ico-Mail "></i>
                        <span>Email Us</span>
                      </div>
                      <div class="desc font-note-sm"><EMAIL></div>
                    </a>
                  </div>
                  <div class="icon-right-box">
                    <i class="ico ico-arrow-right-footer"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 1024- -->
        <div v-else class="contact-us-xs">
          <div class="customer-support" @click="openChatLiveDialog">
            <div class="font-body-md white-95 open-chat-online-box">
              Get Support From OnePlus
              <i class="right-icon ico ico-arrow-right-footer"></i>
            </div>
          </div>
          <div class="media-site">
            <p class="social-media">
              <a tm-eventCategory="Social media" tm-eventAction="pageName" tm-eventLabel="Facebook" href="https://facebook.com/oneplus" class="link-gray" target="_blank">
                <i class="ico ico-facebook-solid svg-icon"></i>
              </a>
              <a tm-eventCategory="Social media" tm-eventAction="pageName" tm-eventLabel="Twitter" href="https://twitter.com/oneplus" class="link-gray" target="_blank">
                <i class="ico ico-twitter-solid svg-icon"></i>
              </a>
              <a tm-eventCategory="Social media" tm-eventAction="pageName" tm-eventLabel="Instagram" href="https://www.instagram.com/oneplus" class="link-gray" target="_blank">
                <i class="ico ico-instagram-footer svg-icon"></i>
              </a>
            </p>
            <div @click="openChooseStore" class="site">
              <i class="ico ico-Language"></i>
              United States
              (English)
            </div>
          </div>
          <div class="payments-img-box">
            <template v-if="countryCookie !== 'in'">
              <img v-for="item in paymentIcons" :src="item" :key="item" alt="">
            </template>
            <template v-else>
              <div v-for="item in indiaIconList" :key="item.name" class="font-note-sm">
                <i :class="item.icon"></i>
                {{ item.name }}

              </div>
            </template>
          </div>
        </div>
      </div>
      <!-- 下层 一套pc 一套移动 -->
      <div v-if="!isFooterXs" class="bottom-container">
        <!-- <div class="div-line"></div> -->
        <div class="copy-left link font-descriptions">
          <ul class="font-note-sm">
            <li>
              <a href="https://www.oneplus.com/us/legal/us-privacy-policy">Privacy Policy</a>
            </li>
            <li>
              <a href="https://www.oneplus.com/us/legal/state-privacy-policy">State Privacy Policy</a>
            </li>
            <li>
              <a href="https://www.oneplus.com/us/legal/state-privacy-policy/do-not-sell-or-share-my-personal-information">Do Not Sell or Share My Personal Information
              </a>
            </li>
            <li>
              <a href="https://www.oneplus.com/us/legal/user-agreement">User Agreement</a>
            </li>
            <li>
              <a href="https://www.oneplus.com/us/legal/terms-of-sales">Terms of Sale</a>
            </li>
            <li>
              <a href="https://security.oneplus.com" rel="nofollow me noopener noreferrer">Security Response
                Center (OneSRC)</a>
            </li>
          </ul>
          <div class="reserved font-note-sm">© 2013 - 2024 OnePlus. All Rights Reserved.</div>
          <!-- <div class="trustee-area">
                      <a href="//privacy.truste.com/privacy-seal/validation?rid=************************************"
                          rel="me noopener noreferrer" target="_blank">
                          <img style="border: none"
                              src="https://image01.oneplus.net/shop/202307/14/1-M00-46-89-CkvTlmSxGVyABjEvAAAMWxG1ttA783.png"
                              alt="TRUSTe">
                      </a>
                  </div> -->
        </div>
        <div class="copy-right" @click="openChooseStore">
          United States (English)
          <i class="ico ico-Language"></i>
        </div>
      </div>
      <div v-else class="bottom-container-xs">
        <ul class="copy-top font-note-sm">
          <li>
            <a href="https://www.oneplus.com/us/legal/us-privacy-policy">Privacy Policy</a>
          </li>
          <li>
            <a href="https://www.oneplus.com/us/legal/state-privacy-policy">State Privacy Policy</a>
          </li>
          <li>
            <a href="https://www.oneplus.com/us/legal/state-privacy-policy/do-not-sell-or-share-my-personal-information">Do Not Sell or Share My Personal Information
            </a>
          </li>
          <li>
            <a href="https://www.oneplus.com/us/legal/user-agreement">User Agreement</a>
          </li>
          <li>
            <a href="https://www.oneplus.com/us/legal/terms-of-sales">Terms of Sale</a>
          </li>
          <li>
            <a href="mailto:<EMAIL>" rel="nofollow me noopener noreferrer">Security Feedback</a>
          </li>
        </ul>
        <div class="copy-middle">
          <div class="reserved font-note-sm">© 2013 - 2024 OnePlus. All Rights Reserved.</div>
          <!-- <div class="trustee-area">
                      <a href="//privacy.truste.com/privacy-seal/validation?rid=************************************"
                          rel="me noopener noreferrer" target="_blank">
                          <img style="border: none"
                              src="https://image01.oneplus.net/shop/202307/14/1-M00-46-89-CkvTlmSxGVyABjEvAAAMWxG1ttA783.png"
                              alt="TRUSTe">
                      </a>
                  </div> -->
        </div>
      </div>
    </div>
    <new-dialog no-padding v-model="chooseStoredialogVisable" :show-cancel-button="false" :show-confirm-button="false" :duration="0.24" :is-need-transition="true" :title="chooseStoredialogVisableTitle" size="large" class="chosse-store-box-dialog-wrapper">
      <choose-store></choose-store>
    </new-dialog>
    <new-dialog v-if="isFooterXs" no-padding v-model="dialogVisable" :show-cancel-button="false" :show-confirm-button="false" :is-show-close-icon="false" :is-use-button-slot="true" class="chat-online-box-dialog-wrapper">
      <div class="chat-online-box-dialog">
        <div class="chat-online-box">
          <!-- 欧美客服 -->
          <div id="customer-service-footer" onclick="onClickCustomerService()" class="item-box" style="display: none;">
            <div class="font-body-md left-msg-box">
              <div class="chat-box">
                <div class="icon-title">
                  <i class="icon-btn-svg">
                    <img src="https://opsg-img-cdn-gl.heytapimg.com/epb/202210/12/NujgdjSAIGaFvgnH.svg" alt="">
                  </i>
                  <span>Contact us</span>
                </div>
                <div class="desc font-note-sm">9 am - 9 pm EST, Mon to Fri; 10 am - 8 pm EST, Sat to Sun</div>
              </div>
              <div class="icon-right-box">
                <i class="ico ico-arrow-right-footer"></i>
              </div>
            </div>
          </div>
          <!-- 印度 -->
          <div v-if="countryCookie === 'in' && isFooterXs" class="item-box">
            <div class="font-body-lg left-msg-box">
              <div class="icon-title">
                <i class="ico ico-WhatsApp"></i>
                <a class="black95" href="https://www.oneplus.com/us/support/contact/whatsapp">
                  <span>WhatsApp</span>
                </a>
              </div>
            </div>
            <div class="icon-right-box">
              <i class="ico ico-arrow-right-footer"></i>
            </div>
          </div>
          <div v-else-if="siteBol && ONLINE_CHAT" id="live-chat-footer" @click="onlineService" class="item-box">
            <div class="font-body-lg left-msg-box">
              <div class="icon-title">
                <i class="ico ico-Message"></i>
                <span>Live Chat</span>
              </div>
            </div>
            <div>
              <i class="ico ico-arrow-right-footer"></i>
            </div>
          </div>
          <!-- <div class="item-box" id="chat-old-class" style="display: none;">
    <div class="font-body-lg left-msg-box">
        <div class="icon-title">
            <i class="ico ico-tab_Communication_Call "></i>
            <a class="black95"
                :href="getHrefTel(phoneTimeObj.phoneNumber)"><span>{{phoneTimeObj.phoneNumber}}</span></a>
        </div>
        <div v-for="(item,index) in phoneTimeObj.phoneTime" :key="index" class="desc font-note-sm">{{item}}</div>
    </div>
    <div>
        <i class="ico ico-arrow-right-footer"></i>
    </div>
</div> -->
          <div class="item-box">
            <div class="font-body-lg left-msg-box">
              <div class="icon-title">
                <i class="ico ico-Mail "></i>
                <span>Email Us</span>
              </div>
              <div class="desc font-note-sm">
                <a href="https://oneplus.custhelp.com/app/ask"><EMAIL></a>
              </div>
            </div>
            <div>
              <i class="ico ico-arrow-right-footer"></i>
            </div>
          </div>
        </div>
    </new-dialog>
  </footer>
</div>
<script id="footer_time_phone_json" type="application/json">
  {
    "phoneTimeObj": {
      "phoneNumber": "+****************",
      "phoneTime": [
        "9 am - 9 pm EST, Mon to Fri;",
        "10 am - 8 pm EST, Sat to Sun"
      ]
    }
  }</script>
<script id="footer_payment_icons_json" type="application/json">
  {
    "paymentIcons": [
      "https://image01.oneplus.net/shop/202306/26/1-M00-45-8C-CkvLh2SY_oSAGH7-AAAX5iaX_00055.svg",
      "https://image01.oneplus.net/shop/202306/26/1-M00-46-22-CkvTlmSZAt2AA3qdAAAKV2TLG30120.svg",
      "https://image01.oneplus.net/shop/202306/26/1-M00-45-8C-CkvLh2SY_sCAYzQkAAAGMWOABJI491.svg",
      "https://image01.oneplus.net/shop/202306/26/1-M00-45-8C-CkvLh2SY_smAHSAeAAAUiYSrcbU903.svg",
      "https://image01.oneplus.net/shop/202306/26/1-M00-46-22-CkvTlmSY_tCAaAV5AAAYILEeNHs553.svg",
      "https://image01.oneplus.net/shop/202306/26/1-M00-46-22-CkvTlmSY_tWAUmb5AAAfRsSQRfw761.svg"
    ]
  }</script>
<style>
  .web-6-class #footer .chat-online-box-dialog-wrapper .chat-online-box-dialog #customer-service-footer .left-msg-box {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
  }
</style>
<script>
  window.addEventListener('DOMContentLoaded', () => {
      // 页面通过配置window.customerServiceShow来决定是否展示悬浮入口
      var isShow = window.customerServiceShow
      var customerDom = document.getElementById('customer-service-footer')
      if (isShow)
        customerDom.style.display = 'flex'
    }
  )
  function onClickCustomerService() {
    // 埋点
    window.dataLayer.push({
      event: 'ga4Event',
      event_name: 'Live_chat',
      event_parameters: {
        current_screen: window.location.href,
        page_content: window.location.href,
        module: 'Live_chat_entry',
        button_name: document.querySelector('#customer-service-footer .icon-title span').innerText,
        title: 1
      }
    })
    // app与web区分跳转链接
    let chat_launch_url = ''
    if (window.OnePlusClientBaseJS !== undefined) {
      chat_launch_url = 'https://oneplus.custhelp.com/app/chat/chat_launch?channel=1'
    } else {
      chat_launch_url = 'https://oneplus.custhelp.com/app/chat/chat_launch?channel=2'
    }
    window.open(chat_launch_url, 'blank')
  }
</script>
<script id="footer_json_data" type="application/json">
  {
    "products": [
      {
        "title": "Phones",
        "productList": [
          {
            "name": "OnePlus 13",
            "link": "https://www.oneplus.com/us/oneplus-13"
          },
          {
            "name": "OnePlus 13R",
            "link": "https://www.oneplus.com/us/oneplus-13r"
          },
          {
            "name": "OnePlus 12",
            "link": "https://www.oneplus.com/us/oneplus-12"
          },
          {
            "name": "OnePlus 12R",
            "link": "https://www.oneplus.com/us/oneplus-12r"
          },
          {
            "name": "OnePlus Open",
            "link": "https://www.oneplus.com/us/oneplus-open"
          },
          {
            "name": "OnePlus Nord N30 5G",
            "link": "https://www.oneplus.com/us/oneplus-n30-5g"
          }
        ]
      },
      {
        "title": "Accessories",
        "productList": [
          {
            "name": "Audio",
            "link": "https://www.oneplus.com/us/store/audio"
          },
          {
            "name": "Tablet",
            "link": "https://www.oneplus.com/us/store/tablet"
          },
          {
            "name": "Wearables",
            "link": "https://www.oneplus.com/us/store/wearables"
          },
          {
            "name": "Case & Protection",
            "link": "https://www.oneplus.com/us/store/cases-protection"
          },
          {
            "name": "Power & Cables",
            "link": "https://www.oneplus.com/us/store/power-cables"
          }
        ]
      },
      {
        "title": "Programs",
        "productList": [
          {
            "name": "Invite Friends",
            "link": "https://www.oneplus.com/us/invite-friends?activityId=21"
          },
          {
            "name": "Trade-in Program",
            "link": "https://www.oneplus.com/us/trade-in"
          },
          {
            "name": "Student Discount Program",
            "link": "https://www.oneplus.com/us/discount-program?verify=education_program"
          },
          {
            "name": "Graduates Discount Program",
            "link": "https://www.oneplus.com/us/graduates-groups?verify=graduate"
          },
          {
            "name": "Employee Discount Program",
            "link": "https://www.oneplus.com/us/employee-groups?verify=employee"
          },
          {
            "name": "Affiliates Program",
            "link": "https://www.oneplus.com/us/affiliate-program"
          }
        ]
      },
      {
        "title": "Support",
        "productList": [
          {
            "name": "OnePlus Store app",
            "link": "https://www.oneplus.com/us/store-app"
          },
          {
            "name": "Shopping FAQs",
            "link": "https://www.oneplus.com/us/support/shopping-help/details"
          },
          {
            "name": "User Manuals",
            "link": "https://www.oneplus.com/us/support/manuals"
          },
          {
            "name": "Software Upgrade",
            "link": "https://www.oneplus.com/us/support/softwareupgrade"
          },
          {
            "name": "Repair Service",
            "link": "https://www.oneplus.com/us/support/repair"
          },
          {
            "name": "Contact Us",
            "link": "https://service.oneplus.com/us/contact"
          }
        ]
      },
      {"title": "Company",
        "productList": [
          {
            "name": "About OnePlus",
            "link": "https://www.oneplus.com/us/brand"
          },
          {
            "name": "Community",
            "link": "https://forums.oneplus.com"
          },
          {
            "name": "Sustainability",
            "link": "https://www.oneplus.com/us/sustainability"
          },
          {
            "name": "Accessibility",
            "link": "https://www.oneplus.com/us/accessibility"
          },
          {
            "name": "Press",
            "link": "https://www.oneplus.com/us/press"
          },
          {
            "name": "Featured Stories",
            "link": "https://www.oneplus.com/us/blog"
          }
        ]
      }
    ]
  }</script>
<link rel="stylesheet" href="https://cdn.opstatics.com/store/********/sitemap/index.css?v=1736268376220" type="text/css">
<script crossorigin src="https://cdn.opstatics.com/store/********/sitemap/index.js?v=1736268376220"></script>
<script type="application/json" id="common-scripts">
  [
    "https://cdn.opstatics.com/store/********/assets/scripts/gtm-business.min.js?v=1736268376220",
    "//js.maxmind.com/js/apis/geoip2/v2.1/geoip2.js?v=1736268376220"
  ]</script>
<script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "OnePlus",
    "alternateName": [
      "OnePlus Official Site"
    ],
    "url": "https://www.oneplus.com/"
  }</script>
<script>
  window.newCheckout = {
    url: "https://www.oneplus.com/us/checkout/payment"
  }
</script>
<script>
  (function() {
      function async_load_resources() {

        function _loadScript(src, callback) {
          var r = false
            , s = document.createElement('script');

          s.type = 'text/javascript';
          s.src = src;
          s.onload = s.onreadystatechange = function() {
            if (!r && (!this.readyState || this.readyState == 'complete')) {
              r = true;
              typeof callback == 'function' && callback();
            }
          }
          ;
          document.head.appendChild(s);
        }

        var scripts = document.getElementById('common-scripts').innerHTML;
        try {
          scripts = JSON.parse(scripts);
        } catch (e) {
          scripts = [];
        }
        for (var i = 0; i < scripts.length; i++) {
          _loadScript(scripts[i]);
        }
      }

      window.addEventListener('load', async_load_resources);
    }
  )();
</script>
<script type="text/javascript" id="741b2a0bd043">
  (function() {
      var siteId = "741b2a0bd043";
      function t(t, e) {
        for (var n = t.split(""), r = 0; r < n.length; ++r)
          n[r] = String.fromCharCode(n[r].charCodeAt(0) + e);
        return n.join("")
      }
      function e(e) {
        return t(e, -h).replace(/%SN%/g, siteId)
      }
      function n(t) {
        for (var e = escape(t) + "=", n = document.cookie.split(";"), r = 0; r < n.length; r++) {
          for (var o = n[r]; " " == o.charAt(0); )
            o = o.substring(1, o.length);
          if (0 === o.indexOf(e))
            return unescape(o.substring(e.length, o.length))
        }
        return null
      }
      function r(t, e) {
        var r = new Date;
        r.setTime(r.getTime() + 15768e7);
        var o, i, a, u = "; expires=" + r.toGMTString();
        if (a = location.host,
        1 === a.split(".").length)
          document.cookie = escape(t) + "=" + escape(e) + u + "; path=/";
        else {
          i = a.split("."),
            i.shift(),
            o = "." + i.join("."),
            document.cookie = escape(t) + "=" + escape(e) + u + "; path=/; domain=" + o;
          var c = n(t);
          null != c && c == e || (o = "." + a,
            document.cookie = escape(t) + "=" + escape(e) + u + "; path=/; domain=" + o)
        }
      }
      function o(t) {
        x.ex = t,
          p(x)
      }
      function i(t, e, n) {
        var r = document.createElement("script");
        r.onerror = n,
          r.onload = e,
          r.type = "text/javascript",
          r.id = "ftr__script",
          r.async = !0,
          r.src = "https://" + t;
        var o = document.getElementsByTagName("script")[0];
        o.parentNode.insertBefore(r, o)
      }
      function a() {
        U(S.uAL),
          setTimeout(u, v, S.uAL)
      }
      function u(t) {
        try {
          var e = t === S.uDF ? m : g;
          i(e, function() {
            k(),
              x.ex = t + S.uS,
              p(x)
          }, function() {
            try {
              k(),
                x.td = 1 * new Date - x.ts,
                x.ex = t + S.uF,
                p(x),
              t === S.uDF && a()
            } catch (e) {
              x.ex = t + S.eUoe,
                p(x)
            }
          })
        } catch (e) {
          x.ex = t + S.eTlu,
            p(x)
        }
      }
      var c = "fort"
        , s = "erTo"
        , d = "ken"
        , f = c + s + d
        , l = "6"
        , h = 3
        , m = e("(VQ(1fgq71iruwhu1frp2vq2(VQ(2vfulsw1mv")
        , g = e("g68x4yj4t5;e6z1forxgiurqw1qhw2vq2(VQ(2vfulsw1mv")
        , v = 10;
      window.ftr__startScriptLoad = 1 * new Date;
      var p = function(t) {
        var e = function(t) {
          return t || ""
        }
          , n = e(t.id) + "_" + e(t.ts) + "_" + e(t.td) + "_" + e(t.ex) + "_" + e(l);
        r(f, n)
      }
        , w = function() {
        str = n(f) || "";
        var t = str.split("_")
          , e = function(e) {
          return t[e] || void 0
        };
        return {
          id: e(0),
          ts: e(1),
          td: e(2),
          ex: e(3),
          vr: e(4)
        }
      }
        , T = function() {
        for (var t = {}, e = "fgu", n = [], r = 0; r < 256; r++)
          n[r] = (r < 16 ? "0" : "") + r.toString(16);
        var o = function(t, e, r, o, i) {
          var a = i ? "-" : "";
          return n[255 & t] + n[t >> 8 & 255] + n[t >> 16 & 255] + n[t >> 24 & 255] + a + n[255 & e] + n[e >> 8 & 255] + a + n[e >> 16 & 15 | 64] + n[e >> 24 & 255] + a + n[63 & r | 128] + n[r >> 8 & 255] + a + n[r >> 16 & 255] + n[r >> 24 & 255] + n[255 & o] + n[o >> 8 & 255] + n[o >> 16 & 255] + n[o >> 24 & 255]
        }
          , i = function() {
          if (window.Uint32Array && window.crypto && window.crypto.getRandomValues) {
            var t = new window.Uint32Array(4);
            return window.crypto.getRandomValues(t),
              {
                d0: t[0],
                d1: t[1],
                d2: t[2],
                d3: t[3]
              }
          }
          return {
            d0: 4294967296 * Math.random() >>> 0,
            d1: 4294967296 * Math.random() >>> 0,
            d2: 4294967296 * Math.random() >>> 0,
            d3: 4294967296 * Math.random() >>> 0
          }
        }
          , a = function() {
          var t = ""
            , e = function(t, e) {
            for (var n = "", r = t; r > 0; --r)
              n += e.charAt(1e3 * Math.random() % e.length);
            return n
          };
          return t += e(2, "0123456789"),
            t += e(1, "123456789"),
            t += e(8, "0123456789")
        };
        return t.safeGenerateNoDash = function() {
          try {
            var t = i();
            return o(t.d0, t.d1, t.d2, t.d3, !1)
          } catch (t) {
            try {
              return e + a()
            } catch (t) {}
          }
        }
          ,
          t.isValidNumericalToken = function(t) {
            return t && t.toString().length <= 11 && t.length >= 9 && parseInt(t, 10).toString().length <= 11 && parseInt(t, 10).toString().length >= 9
          }
          ,
          t.isValidUUIDToken = function(t) {
            return t && 32 === t.toString().length && /^[\d\w]+$/.test(t)
          }
          ,
          t.isValidFGUToken = function(t) {
            return 0 == t.indexOf(e) && t.length >= 12
          }
          ,
          t
      }()
        , S = {
        uDF: "UDF",
        uAL: "UAL",
        mLd: "1",
        eTlu: "2",
        eUoe: "3",
        uS: "4",
        uF: "9",
        tmos: ["T5", "T10", "T15", "T30", "T60"],
        tmosSecs: [5, 10, 15, 30, 60]
      }
        , y = function(t, e) {
        for (var n = S.tmos, r = 0; r < n.length; r++)
          if (t + n[r] === e)
            return !0;
        return !1
      };
      try {
        var x = w();
        try {
          x.id && (T.isValidNumericalToken(x.id) || T.isValidUUIDToken(x.id) || T.isValidFGUToken(x.id)) || (x.id = T.safeGenerateNoDash()),
            x.ts = window.ftr__startScriptLoad,
            p(x);
          var D = new Array(S.tmosSecs.length)
            , U = function(t) {
            for (var e = 0; e < S.tmosSecs.length; e++)
              D[e] = setTimeout(o, 1e3 * S.tmosSecs[e], t + S.tmos[e])
          }
            , k = function() {
            for (var t = 0; t < S.tmosSecs.length; t++)
              clearTimeout(D[t])
          };
          y(S.uDF, x.ex) ? a() : (U(S.uDF),
            setTimeout(u, v, S.uDF))
        } catch (t) {
          x.ex = S.mLd,
            p(x)
        }
      } catch (t) {}
    }
  )();
</script>
<script type="application/json" id="show-ppg-section">
  0</script>
<script>
  var CC_OrgID = "k8vif92e";
  var CC_MerchantID = "gphk088010429800";
  var CC_CreditEnable = parseInt(1);
  function addFingerPrint() {
    var oneplusId = '';

    if (document.cookie) {
      var items = document.cookie.split("; ");
      for (var i = 0; i < items.length; i++) {
        var item = items[i];
        var p = item.indexOf('=');
        var name = item.substring(0, p);
        if (name === 'ONEPLUSID') {
          oneplusId = decodeURIComponent(item.substring(p + 1));
        }
      }
    }

    var CC_OrgIDv = ((typeof CC_OrgID) != "undefined") ? CC_OrgID : '';
    var CC_MerchantIDv = ((typeof CC_MerchantID) != "undefined") ? CC_MerchantID : '';
    var CC_SessionID = oneplusId;

    if (!!CC_SessionID && !document.getElementById('FingerPrintCombox')) {
      var htmlStr = '<p style="background:url(https://h.online-metrix.net/fp/clear.png?org_id=' + CC_OrgIDv + '&amp;session_id=' + CC_MerchantIDv + CC_SessionID + '&amp;m=1)"></p><img src="https://h.online-metrix.net/fp/clear.png?org_id=' + CC_OrgIDv + '&amp;session_id=' + CC_MerchantIDv + CC_SessionID + '&amp;m=2" alt=""> <object type="application/x-shockwave-flash" data="https://h.online-metrix.net/fp/fp.swf?org_id=' + CC_OrgIDv + '&amp;session_id=' + CC_MerchantIDv + CC_SessionID + '" width="1" height="1" id="thm_fp"><param name="movie" value="https://h.online-metrix.net/fp/fp.swf?org_id=' + CC_OrgIDv + '&amp;session_id=' + CC_MerchantIDv + CC_SessionID + '" /></object> <script src="https://h.online-metrix.net/fp/check.js?org_id=' + CC_OrgIDv + '&amp;session_id=' + CC_MerchantIDv + CC_SessionID + '" type="text/javascript"><\/script>';

      var fingerPrintCombox = document.createElement('div');
      fingerPrintCombox.setAttribute('id', 'FingerPrintCombox');
      fingerPrintCombox.setAttribute('style', 'width:0;height:0; overflow:hidden;');
      fingerPrintCombox.innerHTML = htmlStr;

      document.body.appendChild(fingerPrintCombox);
    }

    //credit card fingerprint
    if (!!document.getElementById('FingerPrintCombox') && document.getElementById('FingerPrintCombox').getElementsByTagName('script').length) {
      if (typeof ga == 'function') {
        ga('send', 'event', 'Stats', 'Checkout', 'DFLoaded', '' + CC_MerchantIDv + CC_SessionID, {
          nonInteraction: true
        });
      }
    }

  }
  addFingerPrint();
</script>
<style>
  html .bbs-app-env .hidden-in-app {
    display: block;
  }

  html .bbs-app-env .account-list-container .hidden-in-app {
    display: none;
  }
</style>
<script>
  // firebase 埋点
  if ((typeof window.COMMUNITY_APP_ACCOUNT !== 'undefined' && typeof window.COMMUNITY_APP_ACCOUNT.COMMUNITY_APP_ACCOUNT_LOGIN !== 'undefined' && typeof window.COMMUNITY_APP_ACCOUNT.COMMUNITY_APP_ACCOUNT_LOGIN === 'function') || typeof window.OnePlusClientBaseJS !== 'undefined') {
    function logEvent(name, params) {
      if (!name) {
        return;
      }
      if (window.AnalyticsWebInterface) {
        // Call Android interface
        window.AnalyticsWebInterface.logEvent(name, JSON.stringify(params));
      } else if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.firebase) {
        // Call iOS interface
        var message = {
          command: 'logEvent',
          name: name,
          parameters: params
        };
        window.webkit.messageHandlers.firebase.postMessage(message);
      } else {
        // No Android or iOS interface found
        console.log("No native APIs found.");
      }
    }

    function setUserProperty(name, value) {
      if (!name || !value) {
        return;
      }
      if (window.AnalyticsWebInterface) {
        // Call Android interface
        window.AnalyticsWebInterface.setUserProperty(name, value);
      } else if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.firebase) {
        // Call iOS interface
        var message = {
          command: 'setUserProperty',
          name: name,
          value: value
        };
        window.webkit.messageHandlers.firebase.postMessage(message);
      } else {
        // No Android or iOS interface found
        console.log("No native APIs found.");
      }
    }

    window.onload = function() {
      const screen_key = location.pathname
      if (typeof window.COMMUNITY_APP_ACCOUNT !== 'undefined' && typeof window.COMMUNITY_APP_ACCOUNT.COMMUNITY_APP_ACCOUNT_LOGIN !== 'undefined' && typeof window.COMMUNITY_APP_ACCOUNT.COMMUNITY_APP_ACCOUNT_LOGIN === 'function') {
        logEvent("screen_view", {
          screen_name: screen_key,
          screen_class: 'bbs_screen_class'
        });
      }
    }
  }
</script>
<script>
  function getPageName() {
    var pathArr = location.pathname.split('/')
    var urlName = pathArr.concat([]).pop()
    var pageName = ''

    if (window.isHome) {
      // 首页
      pageName = 'home'
    } else if (urlName === 'store') {
      // 配件主页
      pageName = 'store'
    } else if (/www.+\/store\/phone($|\?)/.test(location.href)) {
      // 手机列表页
      pageName = 'storeProduct'
    } else if (/www.+\/store\/power-cables($|\?)/.test(location.href)) {
      // 电源列表页
      pageName = 'storeProduct'
    } else if (/www.+\/store\/cases-protection($|\?)/.test(location.href)) {
      // 保护壳配件列表页
      pageName = 'storeProduct'
    } else if (/www.+\/store\/audio($|\?)/.test(location.href)) {
      // 耳机列表页
      pageName = 'storeProduct'
    } else if (/www.+\/store\/bundles($|\?)/.test(location.href)) {
      // 套装列表页
      pageName = 'storeProduct'
    } else if (/www.+\/store\/gear($|\?)/.test(location.href)) {
      // 背包列表页
      pageName = 'storeProduct'
    } else if (urlName === 'jcart') {
      // 购物车页
      pageName = 'jcart'
    } else if (urlName === 'onepage') {
      // checkout 页
      pageName = 'checkout'
    } else if (location.pathname.indexOf('jcart/falcon/success') >= 0) {
      // 支付成功页
      pageName = 'paymentSuccess'
    } else if (location.pathname.indexOf('sales/order/history') >= 0 && location.search.indexOf('payment_status=error') >= 0) {
      // 支付失败页
      pageName = 'paymentFail'
    } else if (urlName.indexOf('oneplus-') === 0 && document.getElementById('data-device')) {
      // 手机购买页
      pageName = 'phoneModels'
    } else if (document.getElementById('data-recom')) {
      // 配件购买页
      pageName = 'accessoryDetail'
    } else if (/www.+\/transaction\/pay($|\?)/.test(location.href)) {
      // 收银台页面
      pageName = 'cashRegister'
    } else if (/\/transaction\/pay($|\?)/.test(location.href)) {
      pageName = 'cashRegister'
    } else if (/\/account\//.test(location.href)) {
      // 账号页
      pageName = 'account'
    }
    return pageName
  }
  const screen_path = location.pathname
  const page_group = getPageName()
  const screen_view_params = JSON.stringify({
    "screen_name": screen_path,
    "screen_class": page_group
  })
  if (typeof window.OnePlusAnalyticsWebJs !== 'undefined') {
    window.OnePlusAnalyticsWebJs.logEvent("screen_view", screen_view_params)
  }
</script>
<script type="text/javascript">
  var isXs
  if (window.matchMedia("(max-width: 1024px)").matches) {
    isXs = true;
  } else {
    isXs = false;
  }

  function twofunction() {
    var imgs = document.querySelectorAll('.second-nav-img');
    if (imgs.length) {
      for (var i = 0; i < imgs.length; i++) {
        if (isXs && imgs[i].getAttribute('data-src-mo')) {
          imgs[i].src = imgs[i].getAttribute('data-src-mo');
        } else {
          imgs[i].src = imgs[i].getAttribute('data-src');
        }
      }
    }

    // 旧页面导航图片加载
    var imgsOld = document.querySelectorAll('.navImg');
    if (imgsOld.length) {
      for (var i = 0; i < imgsOld.length; i++) {
        if (isXs) {
          imgsOld[i].src = imgsOld[i].getAttribute('data-src-mo');
        } else {
          imgsOld[i].src = imgsOld[i].getAttribute('data-src');
        }
      }
    }

  }
  // load之后就加载
  function addLoadEvent(func) {
    var oldonload = window.onload;
    if (typeof window.onload != 'function') {
      window.onload = func;
    } else {
      window.onload = function() {
        oldonload();
        func();
      }
    }
  }
  //  到导航栏才加载
  function addLoadIMG(func) {
    var item = document.querySelectorAll("#nav-pages-slide .page")
    for (let i = 0; i < item.length; i++) {
      item[i].addEventListener("mouseover", func, false);
    }
  }
  addLoadIMG(twofunction)
</script>
<script>
  window.omoGetUsetLocation = false
  window.omoGetUserLocation = false
</script>
<!-- 风控指纹接入 -->
<script>
  var oneplusWebFP = oneplusWebFP || undefined;
  var oneplusWebFPConfig = {
    src: 'https://cdn.opstatics.com/store/********/assets/scripts/WebFingerprint.min.js',
    initParams: {
      uploadUrl: 'https://bsp-di-us.heytapmobile.com/v1/js/d',
      appId: 'dd4e4d48b9664f8da2cc0796e4f6f9d3'
    }
  }
  function WebFingerLoad() {
    window.oneplusWebFP = new window.WebFingerprint(window.oneplusWebFPConfig.initParams)
  }
</script>
<script src="https://cdn.opstatics.com/store/********/assets/scripts/WebFingerprint.min.js" onload="WebFingerLoad()" async></script>
<style>
  .web-6-class #footer .bottom-container .copy-left>ul li a {
    color: #828282;
  }

  body .container-text {
    max-width: 1312px;
  }

  #page-footer .bottom-container-xs .copy-top a, #page-footer .bottom-container-xs .copy-middle {
    color: #828282;
  }
</style>
<script defer src="https://cdn.opstatics.com/jimu/global-store/client/chunk-vendors.js?v=1735558881491"></script>
<script defer src="https://cdn.opstatics.com/jimu/global-store/client/chunk-pharos-ui.js?v=1735558881491"></script>
<script defer src="https://cdn.opstatics.com/jimu/global-store/client/oneplus_main.js?v=1735558881491"></script>
</body></html>
