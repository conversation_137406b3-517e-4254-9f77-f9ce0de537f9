import './styles/index.styl'
import 'virtual:uno.css'
import { createApp } from '@tiga/client'
import { createComponent } from '@tiga/client'
import { rpx } from '@/utils'
import { TestPlugin } from './plugins'

const component = createComponent({
  selector: '.zn--wrapper',
  async setup(app, root) {
    const imgs = root.querySelectorAll<HTMLImageElement>(
      '.lazy-img',
    )

    console.log(rpx(200))

    window.addEventListener('resize', () => {
      console.log(rpx(200))
    })

    if (imgs) {
      imgs.forEach((img) => {
        img.src = img.getAttribute('data-src') || ''
        img.decode().then(() => {
          console.log('img decoded')
        })
      })
    }
    const urls = import.meta.glob<string>('~images/s-*.jpg', {
      eager: true,
      import: 'default',
    })
    console.log('urls', urls)
    Object.values(urls).forEach((url) => {
      const image = new Image()
      image.style.width = '200px'
      image.src = url
      image.decode().then(() => {
        document.body.appendChild(image)
      })
    })
  }
})

console.log('env', env)

const app = createApp()
app.use(TestPlugin())
app.register(component)
app.start()

