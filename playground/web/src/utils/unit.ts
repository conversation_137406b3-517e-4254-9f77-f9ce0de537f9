const rpxConfig = [
  {
    base: 360,
    padding: 32,
    query: '(max-width: 649.9px)',
  },
  {
    base: 768,
    padding: 48,
    query: '(min-width: 650px) and (max-width: 1023.9px)',
  },
  {
    base: 1440,
    padding: 128,
    query: '(min-width: 1024px) and (max-width: 1439.9px)',
  },
]

export function rpx(value: number) {
  for(const {query, base, padding} of rpxConfig) {
    if (matchMedia(query).matches) {
      return round(value * (vw(100) - padding ) / (base - padding), 2)
    }
  }
  return value
}

export function vw(value: number) {
  return value * window.innerWidth / 100
}

export function vh(value: number) {
  return value * window.innerHeight / 100
}

function round(num: number, length: number) {
  if (length > 0) {
    const N = 10 ** length
    return Math.round(num * N + Number.EPSILON) / N
  }
  return Math.round(num)
}
