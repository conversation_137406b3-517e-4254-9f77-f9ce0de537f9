{
  "compilerOptions": {
    "lib": [
      "ESNext",
      "DOM"
    ],
    "target": "ESNext",
    "module": "ESNext",
    "moduleDetection": "force",
    "allowJs": true,
    /* Bundler mode */
    "moduleResolution": "Bundler",
    "allowImportingTsExtensions": true,
    "verbatimModuleSyntax": true,
    "isolatedModules": true,
    "preserveConstEnums": true,
    "noEmit": true,
    /* Linting */
    "skipLibCheck": true,
    "strict": true,
    "noFallthroughCasesInSwitch": true,
    "forceConsistentCasingInFileNames": true,
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "~images/*": [
        "./src/assets/images/*"
      ],
      "~videos/*": [
        "./src/assets/videos/*"
      ]
    }
  },
  "include": [
    "src/**/*.ts",
    "vite.config.ts"
  ],
  "exclude": [
    "node_modules"
  ]
}
