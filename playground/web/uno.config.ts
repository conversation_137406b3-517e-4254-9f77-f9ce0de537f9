import {
  defineConfig,
  presetWind3,
  preset<PERSON>ini,
  transformerVariantGroup,
} from 'unocss'

export default defineConfig({
  content: {},
  presets: [
    presetWind3(),
    presetMini({
      variablePrefix: 'zn-',
    }),
  ],
  extractors: [],
  transformers: [transformerVariantGroup()],
  theme: {
    breakpoints: {
      /**
       * @see https://unocss.dev/presets/wind3#breakpoints
       * 举例说明：
       * lt-pad: 小于 pad 这个断点 ===>  @media (max-width: 649.9px)
       * at-pad: 从 pad 这个断点开始，到下一个大于 pad 的断点结束 ===>  @media (min-width: 650px) and (max-width: 1023.9px)
       * pad: 大于等于 pad 这个断点 ===>  @media (min-width: 650px)
       */
      pad: '650px',
      pc: '1024px',
      '2560': '2560px',
    },
  },
})
