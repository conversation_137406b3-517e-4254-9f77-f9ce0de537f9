{"name": "@tiga/vite-plugin-html", "version": "0.0.0", "private": true, "license": "MIT", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}}, "files": ["dist"], "scripts": {"dev": "tsup --watch", "build": "tsup"}, "peerDependencies": {"vite": ">=6.0.0"}, "devDependencies": {"@types/node": "catalog:", "@types/pug": "catalog:", "picocolors": "catalog:", "prettier": "catalog:", "pug": "catalog:", "pug-error": "catalog:", "vite": "catalog:"}}