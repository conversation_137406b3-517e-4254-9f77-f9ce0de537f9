import path from 'node:path'
import fs from 'node:fs/promises'
import prettier from 'prettier'
import type { Plugin, ResolvedConfig } from 'vite'
import type { ResolvedOptions } from '../types.ts'

export function wrapperPlugin(options: ResolvedOptions): Plugin {
  const { template, inject, wrapTo } = options

  let config: ResolvedConfig
  const name = 'vite-plugin-html:wrapper'

  return {
    name,
    enforce: 'post',

    configResolved(resolvedConfig) {
      config = resolvedConfig
    },

    transformIndexHtml: {
      order: 'post',
      async handler(html) {
        const formattedHtml = await prettier.format(html, {
          parser: 'html',
          htmlWhitespaceSensitivity: 'ignore',
        })

        if (!template) {
          return {
            html: formattedHtml,
            tags: inject.tags,
          }
        }

        try {
          const wrapHtml = await fs.readFile(
            path.resolve(config.root, template),
            'utf-8',
          )

          let output = formattedHtml

          switch (wrapTo) {
            case 'main':
              output = wrapHtml.replace('</main>', `${html}</main>`)
              break
            case 'body':
              output = wrapHtml.replace('</body>', `${html}</body>`)
              break
            case 'main-prepend':
              output = wrapHtml.replace('<main>', `<main>${html}`)
              break
            case 'body-prepend':
              output = wrapHtml.replace('<body>', `<body>${html}`)
              break
          }

          return {
            html: output,
            tags: inject.tags,
          }
          // eslint-disable-next-line
        } catch (err: any) {
          err.plugin = name
          throw err
        }
      },
    },
  }
}
