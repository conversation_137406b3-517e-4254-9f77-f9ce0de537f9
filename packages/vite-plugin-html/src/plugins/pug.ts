import pug from 'pug'
import path from 'node:path'
import colors from 'picocolors'
import { getShortName } from '../utils.ts'
import type { ResolvedOptions } from '../types.ts'
import type { Plugin, ResolvedConfig } from 'vite'

export function pugPlugin(options: ResolvedOptions): Plugin {
  const { locals, compileOptions } = options.pugOptions

  let viteConfig: ResolvedConfig
  const name = 'vite-plugin-html:pug'
  const pugFileRegex = /<template src="([^"]*)"><\/template>/g

  const transformHtml = (html: string) => {
    try {
      return html.replace(pugFileRegex, (_, file) => {
        const pugPath = path.resolve(viteConfig.root, file)
        return pug.compileFile(pugPath, compileOptions)(locals)
      })
      // eslint-disable-next-line
    } catch (err: any) {
      err.plugin = name
      err.message = `[pug] ${err.message}`
      throw err
    }
  }

  return {
    name,
    enforce: 'pre',

    configResolved(resolvedConfig) {
      viteConfig = resolvedConfig
    },

    handleHotUpdate({ file, server }) {
      if (file.endsWith('.pug')) {
        const shortFile = getShortName(file, viteConfig.root)

        server.config.logger.info(
          colors.green(`page reload `) + colors.dim(shortFile),
          {
            clear: true,
            timestamp: true,
          },
        )

        server.ws.send({ type: 'full-reload' })
      }
    },

    transform: {
      filter: { id: /\.html$/ },
      handler(html) {
        if (viteConfig.command === 'build') {
          return transformHtml(html)
        }
      },
    },

    transformIndexHtml: {
      order: 'pre',
      handler(html) {
        if (viteConfig.command === 'serve') {
          return transformHtml(html)
        }
      },
    },
  }
}
