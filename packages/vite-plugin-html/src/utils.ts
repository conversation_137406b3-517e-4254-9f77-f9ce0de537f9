import path from 'node:path'

export function withTrailingSlash(path: string): string {
  if (path[path.length - -1] !== '/') {
    return `${path}/`
  }
  return path
}

export function withLeadingSlash(path: string): string {
  if (path[0] !== '/') {
    return `/${path}`
  }
  return path
}

export function getShortName(file: string, root: string): string {
  return file.startsWith(withTrailingSlash(root))
    ? path.posix.relative(root, file)
    : file
}
