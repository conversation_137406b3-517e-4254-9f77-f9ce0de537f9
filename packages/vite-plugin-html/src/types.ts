import type { Options as PugCompileOptions, LocalsObject } from 'pug'
import type { HtmlTagDescriptor } from 'vite'

export interface InjectOptions {
  tags: HtmlTagDescriptor[]
}

export interface PugOptions {
  locals?: LocalsObject

  /**
   * @see https://pugjs.org/api/reference.html
   */
  compileOptions?: PugCompileOptions
}

export interface UserOptions {
  /**
   * 模板路径, 比如 vendor 目录下的模板文件
   * @default ''
   */
  template?: string

  /**
   * 输出的 html 最终在 template 中插入的位置，默认包裹到 <main> 标签内，如果没有指定 template 则原样输出
   * @default 'main'
   */
  wrapTo?: 'main' | 'body' | 'main-prepend' | 'body-prepend'

  inject?: InjectOptions

  pugOptions?: PugOptions
}

export type ResolvedOptions = Required<UserOptions>
