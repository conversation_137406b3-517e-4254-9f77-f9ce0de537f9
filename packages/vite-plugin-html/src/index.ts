import { pugPlugin } from './plugins/pug'
import { wrapperPlugin } from './plugins/wrapper'
import type { Plugin } from 'vite'
import type { UserOptions, ResolvedOptions } from './types'

const defaultOptions: ResolvedOptions = {
  template: '',
  wrapTo: 'main',
  inject: { tags: [] },
  pugOptions: {
    locals: {},
    compileOptions: { pretty: true },
  },
}

export const htmlPlugin = (userOptions: UserOptions = {}): Plugin[] => {
  const options: ResolvedOptions = {
    ...defaultOptions,
    ...userOptions,
    pugOptions: {
      ...defaultOptions.pugOptions,
      ...userOptions.pugOptions,
    },
    inject: {
      ...defaultOptions.inject,
      ...userOptions.inject,
    },
  }

  return [pugPlugin(options), wrapperPlugin(options)]
}
