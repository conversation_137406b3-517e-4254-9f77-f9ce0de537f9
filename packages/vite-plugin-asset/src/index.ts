import path from 'node:path'
import { parse } from 'node-html-parser'
import { withLeadingSlash } from './utils'
import { basePlugin } from './plugins/base'

import type { PluginContext } from 'rollup'
import type { Plugin, ResolvedConfig, ViteDevServer } from 'vite'
import type { AssetPlugin, AssetPluginOptions } from './types'

export * from './types'
export * from './plugins/image'

export function assetPlugin(options: AssetPluginOptions = {}): Plugin {
  const { sources = {} } = options

  const plugins = [basePlugin()]
  if (options.plugins) {
    plugins.unshift(...options.plugins)
  }

  const name = 'vite-plugin-asset'

  let viteConfig: ResolvedConfig

  const processAsset = async (
    url: string,
    pluginContext?: PluginContext,
  ): Promise<string> => {
    const alias = getAlias(viteConfig)
    for (const { find, replacement } of alias) {
      if (url.startsWith(find)) {
        const id = withLeadingSlash(url.replace(find, replacement))
        return applyAs<PERSON><PERSON><PERSON><PERSON>(id, plugins, viteConfig, pluginContext)
      }
    }

    return url
  }

  const transformHtml = async (html: string, pluginContext?: PluginContext) => {
    try {
      const root = parse(html)

      const selectors = Object.keys(sources).join(',')
      const elements = root.querySelectorAll(selectors)

      const promises: Promise<void>[] = []

      elements.forEach((el) => {
        const attrs = sources[el.rawTagName]

        if (attrs) {
          attrs.forEach((attr) => {
            const url = el.getAttribute(attr)

            if (url) {
              promises.push(
                processAsset(url, pluginContext).then((newUrl) => {
                  el.setAttribute(attr, newUrl)
                }),
              )
            }
          })
        }
      })

      await Promise.all(promises)

      return root.toString()
      // eslint-disable-next-line
    } catch (err: any) {
      err.plugin = name
      throw err
    }
  }

  return {
    name,
    enforce: 'pre',

    configResolved(resolvedConfig) {
      viteConfig = resolvedConfig
    },

    configureServer(server: ViteDevServer) {
      plugins
        .map((plugin) => plugin.middleware!)
        .filter(Boolean)
        .forEach((middleware) => {
          server.middlewares.use(middleware)
        })
    },

    load(id) {
      return applyAssetLoad(id, plugins, viteConfig, this)
    },

    transform: {
      filter: { id: /\.html$/ },
      handler(html) {
        if (viteConfig.command === 'build') {
          return transformHtml(html, this)
        }
      },
    },

    transformIndexHtml: {
      order: 'pre',
      handler(html) {
        if (viteConfig.command === 'serve') {
          return transformHtml(html)
        }
      },
    },
  }
}

async function applyAssetHandler(
  id: string,
  plugins: AssetPlugin[],
  viteConfig: ResolvedConfig,
  pluginContext?: PluginContext,
) {
  for (const { filter, handler } of plugins) {
    const canApply = typeof filter === 'function' ? filter(id) : filter.test(id)
    if (canApply) {
      return handler(id, viteConfig, pluginContext)
    }
  }
  throw new Error('No plugin found for asset: ' + id)
}

async function applyAssetLoad(
  id: string,
  plugins: AssetPlugin[],
  viteConfig: ResolvedConfig,
  pluginContext?: PluginContext,
) {
  for (const { filter, load } of plugins) {
    const canApply = typeof filter === 'function' ? filter(id) : filter.test(id)
    if (canApply) {
      return load?.(id, viteConfig, pluginContext)
    }
  }
  throw new Error('No plugin found for asset: ' + id)
}

function getAlias(viteConfig: ResolvedConfig) {
  const { alias } = viteConfig.resolve

  return alias
    .filter(({ find }) => typeof find === 'string')
    .map(({ find, replacement }) => {
      const trailingSlash = replacement.endsWith('/') ? '/' : ''
      return {
        find: find as string,
        replacement:
          path.relative(viteConfig.root, replacement) + trailingSlash,
      }
    })
}
