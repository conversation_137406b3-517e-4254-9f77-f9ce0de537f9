import path from 'node:path'
import fse from 'fs-extra'
import sharp from 'sharp'
import { URLSearchParams } from 'node:url'
import {
  hash,
  generateImageID,
  flatAssetName,
  withLeadingSlash,
} from '../utils'
import { normalizePath } from 'vite'

import type { FormatEnum } from 'sharp'
import type { ResolvedConfig } from 'vite'
import type { PluginContext } from 'rollup'
import type { AssetPlugin } from '../types'

export interface CacheOptions {
  /**
   * 缓存目录
   * @default '.cache/images'
   */
  dir?: string

  /**
   * 是否启用缓存
   * @default true
   */
  enabled?: boolean
}

export interface AssetImageOptions {
  /**
   * 缓存配置
   */
  cacheOptions?: CacheOptions

  /**
   * 是否启用 webp，默认为 true
   */
  enableWebp?: boolean
}

/** 图片处理参数, 从 url 中的 query 获取 */
interface ImageParams {
  /** 压缩质量 (0-100)，默认为 80 */
  quality?: number

  /** 是否启用无损压缩，默认为 false */
  lossless?: boolean

  /** 是否不转换为 webp，默认为 false */
  noWebp?: boolean

  /** 使用原图，不会生成 webp，默认为 false */
  rawImg?: boolean
}

/**
 * 图片处理元数据
 * 包含图片处理过程中所需的所有文件路径、缓冲区数据和处理参数
 */
interface ImageMetadata {
  id: string

  format: keyof FormatEnum

  sourceFilePath: string

  cachedFileName: string

  devFileName: string

  builtFileName: string

  imageParams: ImageParams
}

async function resolveImageInfos(
  id: string,
  viteConfig: ResolvedConfig,
  options: AssetImageOptions,
): Promise<ImageMetadata[]> {
  const { urls, params } = parseId(id)
  const { rawImg, noWebp } = params
  const { enableWebp } = options
  const result: ImageMetadata[] = []

  for (const url of urls) {
    const sourceFilePath = path.join(viteConfig.root, url)
    const imageBuffer = await fse.readFile(sourceFilePath)
    const { format } = await sharp(imageBuffer).metadata()
    const imageHash = hash([imageBuffer])
    const imageName = flatAssetName(url)

    if (rawImg) {
      result.push({
        id: '',
        format: 'webp',
        sourceFilePath,
        cachedFileName: '',
        devFileName: url,
        builtFileName: imageName,
        imageParams: params,
      })
    } else {
      const extname = path.extname(url)

      if (enableWebp && !noWebp) {
        const imageId = generateImageID(
          {
            ...params,
            format: 'webp',
          },
          imageHash,
        )
        result.push({
          id: imageId,
          format,
          sourceFilePath,
          cachedFileName: imageId,
          devFileName: url.replace(extname, `-${imageId.substring(0, 8)}.webp`),
          builtFileName: `${imageName}.webp`,
          imageParams: params,
        })
      }

      const imageId = generateImageID(
        {
          ...params,
          format,
        },
        imageHash,
      )

      result.push({
        id: imageId,
        format,
        sourceFilePath,
        cachedFileName: imageId,
        devFileName: url.replace(
          extname,
          `-${imageId.substring(0, 8)}${extname}`,
        ),
        builtFileName: imageName,
        imageParams: params,
      })
    }
  }

  return result
}

function parseId(id: string): {
  urls: string[]
  params: ImageParams
} {
  const splitUrl = id.split('?')
  const searchParams = new URLSearchParams(splitUrl[1] ?? '')
  const url = splitUrl[0]
  const urls: string[] = [url]

  const params: ImageParams = {
    quality: 80,
    noWebp: false,
    rawImg: false,
    lossless: false,
  }

  for (const [key, value] of searchParams) {
    switch (key) {
      case 'q':
      case 'quality':
        params.quality = Number(value)
        break
      case 'lossless':
        params.lossless = true
        break
      case 'noWebp':
      case 'no-webp':
        params.noWebp = true
        break
      case 'rawImg':
      case 'rawImage':
      case 'raw-img':
      case 'raw-image':
        params.rawImg = true
        params.noWebp = true
        break
    }
  }

  return {
    urls,
    params,
  }
}

async function processImage(
  imageInfo: ImageMetadata,
  cachePath: string,
): Promise<Buffer> {
  const { format, cachedFileName, sourceFilePath, imageParams } = imageInfo

  const { rawImg, quality, noWebp, lossless } = imageParams

  if (rawImg) {
    return fse.readFile(sourceFilePath)
  }

  const cachedPath = path.join(cachePath, cachedFileName)
  const image = sharp(sourceFilePath, { animated: true })

  if (!noWebp) {
    const cachedPath = path.join(cachePath, cachedFileName)
    if (await fse.pathExists(cachedPath)) {
      return fse.readFile(cachedPath)
    } else {
      const webpBuffer = await image
        .clone()
        .toFormat('webp', {
          quality,
          lossless,
        })
        .toBuffer()
      await fse.writeFile(cachedPath, webpBuffer)
      return webpBuffer
    }
  }

  if (await fse.pathExists(cachedPath)) {
    return fse.readFile(cachedPath)
  } else {
    const originBuffer = await image
      .clone()
      .toFormat(format, {
        quality,
        lossless,
      })
      .toBuffer()

    await fse.writeFile(cachedPath, originBuffer)
    return originBuffer
  }
}

function emitBuildAssets(
  pluginContext: PluginContext,
  viteConfig: ResolvedConfig,
  imageInfo: ImageMetadata,
  imageBuffer: Buffer,
) {
  const { builtFileName } = imageInfo

  return pluginContext.emitFile({
    type: 'asset',
    source: imageBuffer,
    name: builtFileName,
    originalFileName: normalizePath(
      path.relative(viteConfig.root, builtFileName),
    ),
  })
}

const defaultOptions = {
  cacheOptions: {
    enabled: true,
    dir: '.cache/images',
  },
  enableWebp: true,
}

export function imagePlugin(userOptions: AssetImageOptions = {}): AssetPlugin {
  const options = { ...userOptions, ...defaultOptions }

  const { cacheOptions } = options

  const pwd = process.cwd()
  const cachePath = path.join(pwd, cacheOptions.dir!)
  fse.ensureDirSync(cachePath)

  const ASSET_PREFIX = '/@img'
  const assetCache = new Map<
    string,
    { assetUrl: string; imageInfo: ImageMetadata }
  >()

  const handleImageInfos = async (
    id: string,
    viteConfig: ResolvedConfig,
    pluginContext?: PluginContext,
  ): Promise<string[]> => {
    const imageInfos = await resolveImageInfos(id, viteConfig, options)
    const assetUrls: string[] = []

    const isBuild = viteConfig.command === 'build'

    for (const imageInfo of imageInfos) {
      const cacheKey = isBuild ? id : `${ASSET_PREFIX}${imageInfo.devFileName}`
      if (assetCache.has(cacheKey)) {
        assetUrls.push(assetCache.get(cacheKey)!.assetUrl)
        continue
      }

      let assetUrl: string
      if (isBuild) {
        const imageBuffer = await processImage(imageInfo, cachePath)
        const referenceId = emitBuildAssets(
          pluginContext!,
          viteConfig,
          imageInfo,
          imageBuffer,
        )
        assetUrl = `__VITE_ASSET__${referenceId}__`
      } else {
        assetUrl = `${ASSET_PREFIX}${imageInfo.devFileName}`
      }

      assetCache.set(assetUrl, { imageInfo, assetUrl })
      assetCache.set(id, { imageInfo, assetUrl })

      assetUrls.push(assetUrl)
    }

    return assetUrls
  }

  return {
    filter: /\.(png|jpg|jpeg|gif)(\?.*)?$/,

    async handler(id, viteConfig, pluginContext) {
      const urls = await handleImageInfos(id, viteConfig, pluginContext)
      return urls[0]
    },

    async middleware(req, res, next) {
      if (req.url && req.url.startsWith(ASSET_PREFIX)) {
        const { imageInfo } = assetCache.get(req.url)!
        const buffer = await processImage(imageInfo, cachePath)

        res.setHeader('Cache-Control', 'public, max-age=86400')
        res.setHeader('Content-Type', `image/${imageInfo.format}`)
        return res.end(buffer)
      }

      next()
    },

    async load(id, viteConfig, pluginContext) {
      id = withLeadingSlash(path.relative(viteConfig.root, id))
      const urls = await handleImageInfos(id, viteConfig, pluginContext)
      return `export default "${urls[0]}"`
    },
  }
}
