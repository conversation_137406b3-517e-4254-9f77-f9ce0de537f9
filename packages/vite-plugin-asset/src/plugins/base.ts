import path from 'node:path'
import fsp from 'node:fs/promises'
import { normalizePath } from 'vite'
import type { AssetPlugin } from '../types'

export function basePlugin(): AssetPlugin {
  return {
    filter: () => true,
    handler: async (id, viteConfig, pluginContext) => {
      const file = path.join(viteConfig.root, id)

      if (pluginContext) {
        const content = await fsp.readFile(file)

        const originalFileName = normalizePath(
          path.relative(viteConfig.root, file),
        )

        const referenceId = pluginContext.emitFile({
          type: 'asset',
          name: path.basename(file),
          originalFileName,
          source: content,
        })

        return `__VITE_ASSET__${referenceId}__`
      }

      return id
    },
  }
}
