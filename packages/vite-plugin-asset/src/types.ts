import type { Plugin<PERSON>ontext, LoadResult } from 'rollup'
import type { ResolvedConfig, Connect } from 'vite'

export interface AssetPlugin {
  filter: RegExp | ((id: string) => boolean)
  handler: (
    id: string,
    viteConfig: ResolvedConfig,
    pluginContext?: PluginContext,
  ) => Promise<string> | string
  middleware?: Connect.NextHandleFunction
  load?: (
    id: string,
    viteConfig: ResolvedConfig,
    pluginContext?: PluginContext,
  ) => Promise<LoadResult> | LoadResult
}

export interface AssetPluginOptions {
  sources?: Record<string, string[]>
  plugins?: AssetPlugin[]
}
