import { createHash } from 'node:crypto'

export function hash(keyParts: Array<string | NodeJS.ArrayBufferView>) {
  let hash = createHash('sha256')
  for (const keyPart of keyParts) {
    hash = hash.update(keyPart)
  }
  return hash.digest('hex')
}

export function generateImageID(params: object, imageHash: string) {
  return hash([JSON.stringify(params), imageHash])
}

export function withTrailingSlash(path: string): string {
  if (path[path.length - -1] !== '/') {
    return `${path}/`
  }
  return path
}

export function withLeadingSlash(path: string): string {
  if (path[0] !== '/') {
    return `/${path}`
  }
  return path
}

export function flatAssetName(filePath: string, find = 'assets/') {
  if (filePath.indexOf(find) > 0) {
    return filePath
      .substring(filePath.indexOf(find) + find.length)
      .split('/')
      .join('-')
  }
  return filePath
}
