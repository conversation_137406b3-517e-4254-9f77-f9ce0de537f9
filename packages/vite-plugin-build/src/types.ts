export type BuildMode = 'preview' | 'complete' | 'patch'

export type PatchJsonType = {
  add: string[]
  delete: string[]
  update: string[]
}

export interface History {
  id: string
  mode: string
  version: string
  createdAt: string
  files: {
    name: string
    hash: string
    timestamp?: number
  }[]
}

export type FileRecord = {
  name: string
  hash: string
  timestamp?: number
}

export type PatchBuildPluginOptions = {
  historyPath: string
  buildMode: BuildMode
  selectHistory?: string
  generateDiff: boolean
  generateOldHtml?: boolean
  version: string
  timestamp?: number
}
