import type { OutputAsset } from 'rollup'

function isIndexHtml(fileName: string) {
  return fileName.endsWith('.html') && fileName.includes('index')
}

function getRawHtml(indexHtmlChunk: OutputAsset) {
  const result =
    typeof indexHtmlChunk.source === 'string'
      ? indexHtmlChunk.source
      : Buffer.isBuffer(indexHtmlChunk.source)
        ? indexHtmlChunk.source.toString('utf-8')
        : ''

  if (!result) {
    throw new Error('index.html 内容为空')
  }

  return result
}

export { isIndexHtml, getRawHtml }
