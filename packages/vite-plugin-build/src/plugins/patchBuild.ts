import type { Plugin } from 'vite'
import type { OutputBundle, OutputAsset } from 'rollup'
import path from 'path'
import fs from 'fs'
import crypto from 'crypto'
import { diffLines } from 'diff'
import type { History, BuildMode, PatchJsonType } from '../types'
import colors from 'picocolors'
import { isIndexHtml, getRawHtml } from '../utils'

const BUILD_MODE_MAP: Record<BuildMode, string> = {
  preview: 'pages',
  patch: 'projects',
  complete: 'projects',
} as const

export interface PatchBuildOptions {
  /**
   * 选择历史版本作为构建基础
   */
  selectHistory?: string
  /**
   * 是否生成差异文件
   */
  generateDiff?: boolean
  /**
   * 是否生成旧版本文件
   */
  generateOldHtml?: boolean
}

export function patchBuildPlugin(params: PatchBuildOptions = {}): Plugin {
  const { generateDiff = true, generateOldHtml = true, selectHistory } = params

  const {
    versionName,
    HISTORY_FILE,
    isPreviewBuild,
    isPatchBuild,
    HISTORY_DIR,
    buildMode,
  } = getBuildInfo()

  const patchJson: PatchJsonType = {
    update: [],
    delete: [],
    add: [],
  }

  let cachedVersion: string | null = null
  let cachedOutDir: string = ''
  const DEFAULT_VERSION = '1.0.0'

  const historyHtmlPath = path.resolve(HISTORY_DIR, 'html')

  return {
    name: 'vite-plugin-build:patch-build',
    apply: 'build',
    enforce: 'post',
    config(config) {
      cachedVersion = getNextVersion({
        historyDir: HISTORY_DIR,
        buildMode,
        isPatchBuild,
        defaultVersion: DEFAULT_VERSION,
      })

      cachedOutDir = getOutDir(cachedVersion)

      return {
        ...config,
        build: {
          outDir: getOutDir(cachedVersion),
        },
      }
    },
    async generateBundle(options, bundle) {
      if (isPreviewBuild) {
        // 预览模式下，没有版本概念
        return
      }

      const { indexHtmlChunk, assetFiles } = getAssets(bundle)
      const { histories } = parseHistoryFile(HISTORY_FILE)
      let lastHistory = histories[histories.length - 1]

      if (!indexHtmlChunk) {
        throw new Error('获取 index.html 失败')
      }

      if (isPatchBuild) {
        lastHistory = await processPatchBuild({
          bundle,
          assetFiles,
          histories,
          lastHistory,
          selectHistory,
          patchJson,
          cachedOutDir,
          plugin: this,
        })
      }

      const newHistory = createAndSaveHistory({
        assetFiles,
        histories,
        buildMode,
        cachedVersion,
        DEFAULT_VERSION,
        HISTORY_FILE,
      })

      addVersionInfoToHtml({
        bundle,
        version: newHistory.version,
      })

      saveHistoryHtml({
        indexHtmlChunk,
        newHistory,
        historyHtmlPath,
      })

      if (lastHistory && generateDiff) {
        generateDiffFiles({
          historyHtmlPath,
          lastHistory,
          newHistory,
          generateOldHtml,
          plugin: this,
          patchJson: patchJson,
        })
      }

      printInfo({
        version: newHistory.version,
        buildMode,
        versionName,
        timestamp: new Date().toLocaleString('zh-CN'),
        outDir: getOutDir(newHistory.version),
      })
    },
    closeBundle() {
      if (isPatchBuild) {
        const fileCount =
          patchJson.update.length +
          patchJson.delete.length +
          patchJson.add.length

        generatePatchJson({
          patchJson,
          cachedOutDir,
        })

        console.log(`共打包了 ${fileCount} 个变动文件`)
      }
    },
  }
}

async function processPatchBuild(params: {
  bundle: OutputBundle
  assetFiles: History['files']
  histories: History[]
  lastHistory: History | undefined
  selectHistory?: string
  patchJson: PatchJsonType
  cachedOutDir: string
  plugin: any
}) {
  const {
    bundle,
    assetFiles,
    histories,
    lastHistory: initialLastHistory,
    selectHistory,
    patchJson,
  } = params

  let lastHistory = initialLastHistory

  if (selectHistory) {
    lastHistory =
      (await getSelectedHistory(histories, selectHistory)) ?? lastHistory
  }

  if (!lastHistory) {
    throw new Error('没有基于的历史版本')
  }

  const { updatedAssets } = processAssets({
    assetFiles,
    lastHistory,
    bundle,
    patchJson,
  })

  checkDeletedFiles({
    lastHistory,
    updatedAssets,
    patchJson,
  })

  return lastHistory
}

function processAssets(params: {
  assetFiles: History['files']
  lastHistory: History
  bundle: OutputBundle
  patchJson: PatchJsonType
}) {
  const { assetFiles, lastHistory, bundle, patchJson } = params

  const updatedAssets = assetFiles.map((file) => {
    const lastFile = lastHistory.files.find((f) => f.name === file.name)

    if (lastFile) {
      if (lastFile.hash === file.hash) {
        delete bundle[file.name]
      } else {
        patchJson.update.push(file.name)
      }
    } else {
      patchJson.add.push(file.name)
    }

    return file
  })

  return { updatedAssets }
}

function checkDeletedFiles(params: {
  lastHistory: History
  updatedAssets: History['files']
  patchJson: PatchJsonType
}) {
  const { lastHistory, updatedAssets, patchJson } = params

  lastHistory.files.forEach((file) => {
    if (!updatedAssets.find((f) => f.name === file.name)) {
      patchJson.delete.push(file.name)
    }
  })
}

function generatePatchJson(params: {
  patchJson: PatchJsonType
  cachedOutDir: string
}) {
  const { patchJson, cachedOutDir } = params

  try {
    const patchJsonPath = path.join(cachedOutDir, 'patch.json')
    fs.mkdirSync(cachedOutDir, { recursive: true })
    fs.writeFileSync(patchJsonPath, JSON.stringify(patchJson), 'utf-8')
  } catch (error) {
    throw new Error(`生成patch.json时出错: ${error}`)
  }
}

function createAndSaveHistory(params: {
  assetFiles: History['files']
  histories: History[]
  buildMode: BuildMode
  cachedVersion: string | null
  DEFAULT_VERSION: string
  HISTORY_FILE: string
}) {
  const {
    assetFiles,
    histories,
    buildMode,
    cachedVersion,
    DEFAULT_VERSION,
    HISTORY_FILE,
  } = params

  const newHistory: History = {
    id: Date.now().toString(),
    mode: buildMode,
    version: cachedVersion || DEFAULT_VERSION,
    createdAt: new Date().toLocaleString('zh-CN'),
    files: assetFiles,
  }

  histories.push(newHistory)
  fs.writeFileSync(HISTORY_FILE, JSON.stringify(histories, null, 2))

  return newHistory
}

function saveHistoryHtml(params: {
  indexHtmlChunk: OutputAsset
  newHistory: History
  historyHtmlPath: string
}) {
  const { indexHtmlChunk, newHistory, historyHtmlPath } = params

  fs.mkdirSync(historyHtmlPath, { recursive: true })

  fs.writeFileSync(
    path.resolve(historyHtmlPath, `${newHistory.version}.html`),
    indexHtmlChunk.source,
  )
}

function generateDiffFiles(params: {
  historyHtmlPath: string
  lastHistory: History
  newHistory: History
  generateOldHtml: boolean
  patchJson: PatchJsonType
  plugin: any
}) {
  const {
    historyHtmlPath,
    lastHistory,
    newHistory,
    generateOldHtml,
    plugin,
    patchJson: patchJson,
  } = params

  try {
    const diffContent = getDiff(
      path.resolve(historyHtmlPath, `${lastHistory.version}.html`),
      path.resolve(historyHtmlPath, `${newHistory.version}.html`),
    )
    if (diffContent) {
      console.log('\n发现 html 差异，生成 html-changes.html')
      patchJson.update.push('index.html')
    }

    plugin.emitFile({
      type: 'asset',
      fileName: 'html-changes.html',
      source: diffContent,
    })

    if (generateOldHtml) {
      const oldHtml = fs.readFileSync(
        path.resolve(historyHtmlPath, `${lastHistory.version}.html`),
      )

      plugin.emitFile({
        type: 'asset',
        fileName: 'index-old.html',
        source: oldHtml,
      })
    }
  } catch (error) {
    throw new Error(`生成差异文件时出错: ${error}`)
  }
}

function getAssets(bundle: OutputBundle): {
  indexHtmlChunk: OutputAsset | undefined
  assetFiles: History['files']
} {
  const assetFiles: History['files'] = []

  let indexHtmlChunk: OutputAsset | undefined

  for (const [fileName, chunk] of Object.entries(bundle)) {
    if (isIndexHtml(fileName)) {
      indexHtmlChunk = chunk as OutputAsset
      continue
    }

    if (chunk.type === 'asset' || chunk.type === 'chunk') {
      const content = chunk.type === 'asset' ? chunk.source : chunk.code
      const hash = generateAssetHash(content)
      assetFiles.push({ name: fileName, hash })
    }
  }

  return { indexHtmlChunk, assetFiles }
}

function getOutDir(version: string): string {
  const { versionName, buildMode } = getBuildInfo()

  if (!versionName || !buildMode) {
    throw new Error('版本名称或构建模式获取失败')
  }

  const baseDir = `./dist/${BUILD_MODE_MAP[buildMode]}/${versionName}`
  const versionDir = `/${versionName}_v${version}_${buildMode}`

  const outDir = buildMode === 'preview' ? baseDir : baseDir + versionDir

  return outDir
}

function getNextVersion(params: {
  historyDir: string
  buildMode: BuildMode
  isPatchBuild: boolean
  defaultVersion: string
}): string {
  const { historyDir, buildMode, isPatchBuild, defaultVersion } = params

  if (!fs.existsSync(historyDir)) {
    if (isPatchBuild) {
      throw new Error('没有历史版本目录，Patch Build 失败')
    }
    return defaultVersion
  }

  const { histories, lastVersion } = parseHistoryFile(
    path.join(historyDir, 'packages.json'),
  )

  const versions = histories.map((h) => h.version)

  if (isPatchBuild && histories.length === 0) {
    throw new Error('没有有效的版本文件，Patch Build 失败')
  }

  if (versions.length === 0) {
    return defaultVersion
  }

  const parts = lastVersion.split('.').map(Number)

  switch (buildMode) {
    case 'complete':
      parts[0] += 1
      parts[1] = 0
      parts[2] = 0
      break
    case 'patch':
      parts[1] += 1
      parts[2] = 0
      break
    case 'preview':
      break
  }

  return parts.join('.')
}

async function getSelectedHistory(
  histories: History[],
  selectHistory: string,
): Promise<History | undefined> {
  const target = histories.find((h) => h.version === selectHistory)
  if (!target) {
    throw new Error('所选增量包不存在')
  }
  console.log(`选中了 v${target.version} 作为增量包构建基础`)

  return target
}

function printInfo(versionInfo: Record<string, string>) {
  console.log(`\n📦 Build Configuration:`)
  console.log(`   Environment: ${versionInfo.versionName}`)
  console.log(`   Build Mode: ${versionInfo.buildMode}`)
  if (versionInfo.buildMode !== 'preview') {
    console.log(`   Version: ${versionInfo.version}`)
  }
  console.log(`   Output: ${versionInfo.outDir}`)
  console.log(`   Timestamp: ${versionInfo.timestamp}\n`)
}

export function getDiff(oldFilePath: string, newFilePath: string): string {
  const oldContent = fs.readFileSync(oldFilePath, 'utf-8')
  const newContent = fs.readFileSync(newFilePath, 'utf-8')

  const diffs = diffLines(oldContent, newContent)

  let result = ''

  diffs.forEach((part) => {
    if (part.added) {
      result += `{ADD::BEGIN}\n${part.value}{ADD::END}\n`
    } else if (part.removed) {
      result += `{REMOVE::BEGIN}\n${part.value}{REMOVE::END}\n`
    } else {
      result += part.value
    }
  })

  return result
}

function generateAssetHash(content: string | Buffer | Uint8Array): string {
  const md5 = crypto.createHash('md5')
  const input = typeof content === 'string' ? content : Buffer.from(content)
  md5.update(input)
  return md5.digest('hex')
}

function parseHistoryFile(historyFile: string) {
  if (!historyFile) {
    throw new Error(`historyFile 参数是必须的，但传入了：${historyFile}`)
  }

  const historyDir = path.dirname(historyFile)

  if (!fs.existsSync(historyDir)) {
    fs.mkdirSync(historyDir, { recursive: true })
  }

  if (!fs.existsSync(historyFile)) {
    fs.writeFileSync(historyFile, JSON.stringify([], null, 2), 'utf-8')
    console.log(
      `\n\n ${colors.green('➜')} 未发现版本历史文件，已创建: ${historyFile}`,
    )
  } else if (fs.existsSync(historyFile) && !fs.statSync(historyFile).isFile()) {
    throw new Error(`historyFile 必须是一个 json 文件：${historyFile}`)
  }

  const histories: History[] = fs.existsSync(historyFile)
    ? JSON.parse(fs.readFileSync(historyFile).toString())
    : []

  const lastVersion = histories[histories.length - 1]?.version || '0.0.0'

  return { histories, lastVersion }
}

function getBuildInfo() {
  const { __VERSION_NAME__, __BUILD_MODE__ } = JSON.parse(
    process.env.__INJECT_ENVS__ || '{}',
  )
  const versionName = __VERSION_NAME__ || undefined
  const buildMode = (__BUILD_MODE__ as BuildMode) || undefined
  const isPatchBuild = buildMode === 'patch'
  const HISTORY_DIR = path.resolve('./', '.history', versionName)
  const HISTORY_FILE = path.join(HISTORY_DIR, 'packages.json')
  const isPreviewBuild = buildMode === 'preview'

  return {
    versionName,
    buildMode,
    isPatchBuild,
    HISTORY_DIR,
    HISTORY_FILE,
    isPreviewBuild,
  }
}

function addVersionInfoToHtml(params: {
  bundle: OutputBundle
  version: string
}) {
  const { bundle, version } = params
  const indexHtmlFileName = Object.keys(bundle).find(isIndexHtml)

  if (!indexHtmlFileName) {
    throw new Error('获取 index.html 失败')
  }

  const indexHtmlChunk = bundle[indexHtmlFileName] as OutputAsset

  if (!indexHtmlChunk || indexHtmlChunk.type !== 'asset') {
    throw new Error('index.html 类型错误')
  }

  const rawHtml = getRawHtml(indexHtmlChunk)

  const versionComment = `<!-- Version: ${version} -->\n`
  let updatedHtml = versionComment + rawHtml

  const znWrapperRegex =
    /(<div[^>]*class=["'][^"']*zn--wrapper[^"']*["'][^>]*)(>)/g

  updatedHtml = updatedHtml.replace(
    znWrapperRegex,
    (match, openingTag, closingBracket) => {
      if (openingTag.includes('data-version=')) {
        return (
          openingTag.replace(
            /data-version=["'][^"']*["']/,
            `data-version="${version}"`,
          ) + closingBracket
        )
      }
      return `${openingTag} data-version="${version}"${closingBracket}`
    },
  )

  indexHtmlChunk.source = updatedHtml
}
