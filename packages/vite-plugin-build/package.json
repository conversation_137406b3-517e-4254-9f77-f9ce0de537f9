{"name": "@tiga/vite-plugin-build", "version": "0.0.0", "private": true, "license": "MIT", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}}, "files": ["dist"], "scripts": {"dev": "tsup --watch", "build": "tsup"}, "dependencies": {"mkdirp": "catalog:"}, "devDependencies": {"picocolors": "catalog:", "@types/mkdirp": "catalog:", "tsup": "catalog:", "typescript": "catalog:", "vite": "catalog:", "diff": "^8.0.2", "rollup": "catalog:"}}