{"name": "@tiga/eslint-config", "version": "0.0.0", "private": "true", "type": "module", "license": "MIT", "exports": {"./base": "./base.js", "./react-internal": "./react-internal.js"}, "devDependencies": {"@eslint/js": "catalog:", "eslint": "catalog:", "eslint-config-prettier": "catalog:", "eslint-plugin-only-warn": "catalog:", "eslint-plugin-react": "catalog:", "eslint-plugin-react-hooks": "catalog:", "eslint-plugin-turbo": "catalog:", "globals": "catalog:", "typescript-eslint": "catalog:"}}