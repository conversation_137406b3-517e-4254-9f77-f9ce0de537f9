export interface AppOptions {
  rootSelector?: string
}

export interface Plugin {
  name: string
  install: (app: App) => void | Promise<void>
}

// eslint-disable-next-line
export interface TigaPlugins {}

export interface ComponentOptions {
  selector: string
  // eslint-disable-next-line
  setup: (app: App, root: HTMLElement, ...args: any[]) => void
}

export function createComponent(options: ComponentOptions): ComponentOptions {
  return options
}

export class App {
  private root: HTMLElement
  private _isStarted = false
  private _plugins: Plugin[] = []
  private _components: ComponentOptions[] = []
  plgs: TigaPlugins = {}

  constructor(options: AppOptions = {}) {
    this.root = document.querySelector(options.rootSelector || 'body')!
  }

  use(plugin: Plugin): App {
    if (this._isStarted) {
      plugin.install(this)
    } else {
      this._plugins.push(plugin)
    }
    return this
  }

  register(component: ComponentOptions): App {
    if (this._isStarted) {
      this._initComponent(component)
    } else {
      this._components.push(component)
    }
    return this
  }

  async start(): Promise<void> {
    await this._initPlugins()
    this._initComponents()
    this._isStarted = true
  }

  private _initComponent(component: ComponentOptions): void {
    const { selector, setup } = component
    const elements = document.querySelectorAll(selector)
    elements.forEach((el) => {
      setup(this, el as HTMLElement)
    })
  }

  private _initComponents(): void {
    for (const component of this._components) {
      this._initComponent(component)
    }
  }

  private _initPlugins() {
    const promises = this._plugins.map((plugin) => {
      try {
        return plugin.install(this)
      } catch (err: unknown) {
        if (err instanceof Error) {
          err.message = `[plugin:${plugin.name}] ${err.message}`
          throw err
        }
        throw new Error(`[plugin:${plugin.name}] ${err}`)
      }
    })
    return Promise.all(promises)
  }
}

export function createApp(options: AppOptions = {}): App {
  return new App(options)
}
