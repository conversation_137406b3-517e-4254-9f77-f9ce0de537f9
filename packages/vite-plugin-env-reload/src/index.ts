import type { Plugin, ResolvedConfig } from 'vite'

export function envReloadPlugin(): Plugin {
  let config: ResolvedConfig
  return {
    name: 'vite-plugin-env-restart',
    apply: 'serve',
    configResolved(resolvedConfig) {
      config = resolvedConfig
    },
    configureServer(server) {
      if (!config.define) {
        return
      }
      const env = config.define.env
      server.watcher.add('./src/.env.yaml')
      server.watcher.on('change', async (file) => {
        if (file.endsWith('.env.yaml')) {
          // TODO: 将 env 注入到 process.env 中,

          server.restart()
        }
      })
    },
  }
}
