import { defineConfig } from 'tsup'

export default defineConfig({
  entry: ['src/cli.ts'],
  format: ['esm'],
  dts: true,
  clean: true,
  banner: {
    js: `import { createRequire } from 'module'; const require = createRequire(import.meta.url);`,
  },
  // 复制模板文件到构建输出
  onSuccess: async () => {
    const fs = await import('fs-extra')
    const path = await import('path')

    const templatesSource = path.resolve('templates')
    const templatesTarget = path.resolve('dist/templates')

    if (await fs.pathExists(templatesSource)) {
      await fs.copy(templatesSource, templatesTarget)
      console.log('Templates copied to dist/')
    }
  },
})
