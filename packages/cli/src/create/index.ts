import fs from 'fs-extra'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import * as prompts from '@clack/prompts'
import colors from 'picocolors'
import spawn from 'cross-spawn'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

export interface CreateOptions {
  projectName?: string
  template?: string
  skipInstall?: boolean
}

const TEMPLATES = {
  basic: {
    name: 'Basic',
    description: 'A basic Tiga project with essential setup',
  },
}

export async function createProject(options: CreateOptions = {}) {
  console.log()
  prompts.intro(colors.cyan('🚀 Welcome to Tiga Project Creator'))

  let projectName = options.projectName
  let template = options.template

  // 获取项目名称
  if (!projectName) {
    const nameResult = await prompts.text({
      message: 'Please enter the project name:',
      placeholder: 'my-project',
      validate: (value) => {
        if (!value) return 'Project name is required'
        if (!/^[a-zA-Z0-9-_]+$/.test(value)) {
          return 'Project name only supports letters, numbers, - and _'
        }
        return
      },
    })

    if (prompts.isCancel(nameResult)) {
      prompts.cancel('Operation cancelled')
      process.exit(0)
    }

    projectName = nameResult as string
  }

  if (!template) {
    const templateResult = await prompts.select({
      message: 'Select a template:',
      options: Object.entries(TEMPLATES).map(([key, value]) => ({
        value: key,
        label: value.name,
        hint: value.description,
      })),
    })

    if (prompts.isCancel(templateResult)) {
      prompts.cancel('Operation cancelled')
      process.exit(0)
    }

    template = templateResult as string
  }

  // 检查目标目录
  const targetDir = path.resolve(process.cwd(), projectName!)
  if (await fs.pathExists(targetDir)) {
    prompts.cancel(`Directory "${projectName}" already exists.`)
    process.exit(1)
  }

  // 创建项目
  const spinner = prompts.spinner()
  spinner.start('Creating project...')

  try {
    await copyTemplate(template, projectName, targetDir)
    await replaceTemplateVariables(targetDir, { PROJECT_NAME: projectName! })

    spinner.stop('Project created successfully!')

    // 安装依赖
    if (!options.skipInstall) {
      const shouldInstall = await prompts.confirm({
        message: 'Install dependencies?',
        initialValue: true,
      })

      if (!prompts.isCancel(shouldInstall) && shouldInstall) {
        spinner.start('Installing dependencies...')
        await installDependencies(targetDir)
        spinner.stop('Dependencies installed!')
      }
    }

    // 完成提示
    prompts.outro(
      colors.green(`✅ Project ${projectName} created successfully!\n\n`) +
        colors.cyan('Next steps:\n') +
        colors.dim(`  cd ${projectName}\n`) +
        colors.dim(`  pnpm dev\n`),
    )
  } catch (error) {
    spinner.stop('Failed to create project')
    console.error(colors.red('Error:'), error)
    process.exit(1)
  }
}

async function copyTemplate(
  template: string,
  projectName: string,
  targetDir: string,
) {
  // 在构建后的环境中，模板文件在 dist/templates 目录
  let templateDir = path.resolve(__dirname, 'templates', template)

  // 如果在 dist 目录中找不到模板，尝试从源码目录查找（开发环境）
  if (!(await fs.pathExists(templateDir))) {
    templateDir = path.resolve(__dirname, '../../templates', template)
  }

  if (!(await fs.pathExists(templateDir))) {
    throw new Error(
      `Template "${template}" not found. Searched in: ${templateDir}`,
    )
  }

  await fs.copy(templateDir, targetDir)
}

async function replaceTemplateVariables(
  targetDir: string,
  variables: Record<string, string>,
) {
  const files = await getAllFiles(targetDir)

  for (const file of files) {
    const content = await fs.readFile(file, 'utf-8')
    let newContent = content

    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g')
      newContent = newContent.replace(regex, value)
    }

    if (newContent !== content) {
      await fs.writeFile(file, newContent)
    }
  }
}

async function getAllFiles(dir: string): Promise<string[]> {
  const files: string[] = []
  const items = await fs.readdir(dir)

  for (const item of items) {
    const fullPath = path.join(dir, item)
    const stat = await fs.stat(fullPath)

    if (stat.isDirectory()) {
      files.push(...(await getAllFiles(fullPath)))
    } else {
      files.push(fullPath)
    }
  }

  return files
}

async function installDependencies(targetDir: string) {
  return new Promise<void>((resolve, reject) => {
    const child = spawn('pnpm', ['install'], {
      cwd: targetDir,
      stdio: 'inherit',
    })

    child.on('close', (code) => {
      if (code !== 0) {
        reject(new Error(`pnpm install failed with code ${code}`))
      } else {
        resolve()
      }
    })

    child.on('error', reject)
  })
}
