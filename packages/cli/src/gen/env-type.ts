import fs from 'fs-extra'
import path from 'node:path'
import dayjs from 'dayjs'
import colors from 'picocolors'
import { loadEnvData } from '../env'

const pwd = process.cwd()
const OUTPUT_PATH = path.resolve(pwd, './src/@types/env.yaml.d.ts')

// eslint-disable-next-line
function assignEnvType(envTypes: Record<string, any>, key: string, value: any) {
  envTypes[key] ||= []

  switch (typeof value) {
    case 'boolean':
      envTypes[key].push('boolean')
      break
    case 'number':
      envTypes[key].push(value)
      break
    case 'object':
      envTypes[key].push(JSON.stringify(value))
      break
    default:
      envTypes[key].push(`'${value}'`)
      break
  }
}

export async function genEnvType() {
  const envData = await loadEnvData()

  // eslint-disable-next-line
  const envTypes: Record<string, any[]> = {}

  for (const key in envData.global) {
    const value = envData.global[key]
    assignEnvType(envTypes, key, value)
  }

  for (const key in envData.versions) {
    const version = envData.versions[key]

    for (const key in version) {
      const value = version[key]
      assignEnvType(envTypes, key, value)
    }
  }

  for (const key in envTypes) {
    envTypes[key] = [...new Set(envTypes[key])]
  }

  const output =
    '/**\n' +
    ` * ${dayjs().format('YYYY-MM-DD HH:mm:ss')} \n` +
    ' * 本文件由 tiga-cli 根据 .env.yaml 自动生成，请勿直接修改\n' +
    ' */\n\n' +
    'declare const env: {\n' +
    '  isDev: boolean\n' +
    '  isProd: boolean\n' +
    '  isPreview: boolean\n' +
    '  isDelivery: boolean\n' +
    `${Object.entries(envTypes)
      .map(([variable, type]) => `  ${variable}: ${type.join(' | ')}\n`)
      .join('')}` +
    '}\n\n'

  await fs.ensureDir(path.dirname(OUTPUT_PATH))
  await fs.writeFile(OUTPUT_PATH, output)

  console.log(
    `${colors.green('update')} ${OUTPUT_PATH.replace(pwd, '').substring(1)}`,
  )
}
