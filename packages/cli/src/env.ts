import fs from 'node:fs/promises'
import path from 'node:path'
import yaml from 'js-yaml'
import colors from 'picocolors'

export interface EnvData {
  global: Record<string, unknown>
  versions: Record<string, Record<string, unknown>>
}

const ENV_PATH = path.resolve(process.cwd(), 'src', '.env.yaml')

export async function loadEnvData(): Promise<EnvData> {
  try {
    const data = await fs.readFile(ENV_PATH, 'utf-8')
    const envData = yaml.load(data) as Record<string, unknown>

    const result: EnvData = {
      global: {},
      versions: {},
    }

    for (const key in envData) {
      const value = envData[key]
      if (
        value !== null &&
        !Array.isArray(value) &&
        typeof value === 'object'
      ) {
        result.versions[key] = value as Record<string, unknown>
        result.versions[key].__VERSION__ = key
      } else {
        result.global[key] = value
      }
    }

    return result
  } catch (error) {
    console.log(colors.red(`文件 .env.yaml 解析失败: ${error}`))
    process.exit(1)
  }
}
