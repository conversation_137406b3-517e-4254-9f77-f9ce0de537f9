type BuildMode = 'preview' | 'patch' | 'complete'

type CommandType = 'dev' | 'build'

interface BuildCommandOption {
  versionName?: string
  buildMode?: BuildMode
  dev: boolean
  selectHistory?: string
  imageRaw?: boolean
  buildAll?: boolean
}

type InjectEnvTypes = {
  __VERSION_NAME__: string | undefined
  __BUILD_MODE__: BuildMode | undefined
}

export type { BuildCommandOption, BuildMode, InjectEnvTypes, CommandType }
