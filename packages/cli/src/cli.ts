#!/usr/bin/env node

import { Command, Option } from 'commander'
import spawn from 'cross-spawn'
import * as prompts from '@clack/prompts'
import colors from 'picocolors'
import { loadEnvData } from './env'
import { genEnvType } from './gen/env-type'
import { createProject } from './create'
import type { BuildCommandOption, CommandType } from './types'

const program = new Command()

const versionOption = new Option('-e, --version-name <name>', '指定目标版本')

const buildAllEnvOption = new Option('-a, --build-all', '是否全量构建')

program.name('tiga').description('Tiga CLI for build and dev').version('0.0.1')

program
  .command('create [project-name]')
  .description('创建新的 Tiga 项目')
  .option('-t, --template <template>', '指定模板类型', 'basic')
  .option('--skip-install', '跳过依赖安装')
  .action(async (projectName, options) => {
    await createProject({
      projectName,
      template: options.template,
      skipInstall: options.skipInstall,
    })
  })

program
  .command('dev')
  .description('启动开发服务器')
  .addOption(versionOption)
  .action(createCommandHandler('dev'))

program
  .command('build:preview')
  .description('构建预览版本')
  .addOption(versionOption)
  .addOption(buildAllEnvOption)
  .action(createCommandHandler('build', 'preview'))

program
  .command('build:patch')
  .description('构建补丁版本')
  .addOption(versionOption)
  .addOption(buildAllEnvOption)
  .action(createCommandHandler('build', 'patch'))

program
  .command('build:complete')
  .description('构建完整版本')
  .addOption(versionOption)
  .addOption(buildAllEnvOption)
  .action(createCommandHandler('build', 'complete'))

program.command('gen:env').description('生成环境类型').action(genEnvType)

async function chooseEnv(versionNames: string[]) {
  const versionName = await prompts.select({
    message: '请选择项目版本：',
    options: versionNames.map((name) => ({
      value: name,
      label: name,
    })),
  })

  if (prompts.isCancel(versionName)) {
    prompts.cancel('操作中断')
    process.exit(0)
  }

  return versionName as string
}

function createCommandHandler(
  command: CommandType,
  buildMode?: BuildCommandOption['buildMode'],
) {
  return async (options: BuildCommandOption) => {
    const envData = await loadEnvData()
    const versionNames = Object.keys(envData.versions)

    if (options.buildAll) {
      // ？如果某一个版本构建失败，其他版本会继续构建，版本号会继续累加
      for (const versionName of versionNames) {
        const injectEnvs = {
          __VERSION_NAME__: versionName,
          __BUILD_MODE__: buildMode,
          ...envData.global,
          ...envData.versions[versionName],
        }

        runVite(command, injectEnvs)
      }
    } else {
      let versionName = options.versionName

      if (versionName === undefined && versionNames.length > 1) {
        versionName = await chooseEnv(versionNames)
      }

      if (!versionNames.includes(versionName!)) {
        console.log(
          colors.red(`版本 "${versionName}" 不存在，请从以下版本中选择:\n`),
        )
        versionName = await chooseEnv(versionNames)
      }

      const injectEnvs = {
        __VERSION_NAME__: versionName,
        __BUILD_MODE__: buildMode,
        ...envData.global,
        ...envData.versions[versionName!],
      }

      if (command === 'dev') {
        console.log(
          `\n${colors.green('➜')} start dev server for ${colors.cyan(
            versionName,
          )}...`,
        )
      }

      const { status } = runVite(command, injectEnvs)

      process.exit(status ?? 0)
    }
  }
}

function runVite(command: CommandType, injectEnvs: Record<string, unknown>) {
  return spawn.sync('vite', [command], {
    stdio: 'inherit',
    env: {
      ...process.env,
      __INJECT_ENVS__: JSON.stringify(injectEnvs),
    },
  })
}

program.parse(process.argv)
