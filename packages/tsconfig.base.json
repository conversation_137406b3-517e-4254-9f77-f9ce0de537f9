{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "es2022", "lib": ["es2022", "dom"], "module": "ESNext", "moduleDetection": "force", "allowJs": true, "moduleResolution": "<PERSON><PERSON><PERSON>", "allowImportingTsExtensions": true, "verbatimModuleSyntax": true, "isolatedModules": true, "preserveConstEnums": true, "noEmit": true, "skipLibCheck": true, "strict": true, "noFallthroughCasesInSwitch": true, "forceConsistentCasingInFileNames": true}, "include": ["**/*.ts"], "exclude": ["node_modules"]}