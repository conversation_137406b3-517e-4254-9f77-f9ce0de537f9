import type { Plugin } from 'postcss'

function rpx2CalcPlugin(): Plugin {
  return {
    postcssPlugin: 'postcss-rpx-calc',
    Declaration(decl) {
      // 支持负号和小数
      if (/(-?\d*\.?\d+)rpx/.test(decl.value)) {
        decl.value = decl.value.replace(
          /(-?\d*\.?\d+)rpx/g,
          'calc(var(--rpx, 1px) * $1)',
        )
      }
    },
  }
}

rpx2CalcPlugin.postcss = true

export default rpx2CalcPlugin
